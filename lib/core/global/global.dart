import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

ValueNotifier<Map<String, bool>> socialPlatformsStatus = ValueNotifier<Map<String, bool>>({
  // 'TWITTER': PrefObj.preferences?.get(PrefKeys.TWITTER) ?? false,
  'VIMEO': Prefobj.preferences?.get(Prefkeys.VIMEO) ?? false,
  'FACEBOOK': Prefobj.preferences?.get(Prefkeys.FACEBOOK) ?? false,
  'INSTAGRAM': Prefobj.preferences?.get(Prefkeys.INSTAGRAM) ?? false,
  'THREAD': Prefobj.preferences?.get(Prefkeys.THREAD) ?? false,
  'LINKEDIN': Prefobj.preferences?.get(Prefkeys.LINKEDIN) ?? false,
  'PINTEREST': Prefobj.preferences?.get(Prefkeys.PINTEREST) ?? false,
  'TUMBLR': Prefobj.preferences?.get(Prefkeys.TUMBLR) ?? false,
  'REDDIT': Prefobj.preferences?.get(Prefkeys.REDDIT) ?? false,
  'YOUTUBE': Prefobj.preferences?.get(Prefkeys.YOUTUBE) ?? false,
  'TIKTOK': Prefobj.preferences?.get(Prefkeys.TIKTOK) ?? false,
  'X': Prefobj.preferences?.get(Prefkeys.X) ?? false,
  'TELEGRAM': Prefobj.preferences?.get(Prefkeys.TELEGRAM) ?? false,
  'MASTODON': Prefobj.preferences?.get(Prefkeys.MASTODON) ?? false,
  // 'DAILYMOTION': PrefObj.preferences?.get(PrefKeys.DAILYMOTION) ?? false,
});
void updatePlatformStatus(String platformKey, bool newStatus) {
  // Create a copy of the current map
  Map<String, bool> updatedMap = Map<String, bool>.from(socialPlatformsStatus.value);

  // Update the status for the given platform
  updatedMap[platformKey] = newStatus;

  // Print the updated map for debugging
  Logger.lOG("Updated Map: $updatedMap");

  // Set the new map to trigger the ValueNotifier's listeners
  socialPlatformsStatus.value = updatedMap; // This replaces the entire map
}

ValueNotifier<bool> shouldInitialize = ValueNotifier<bool>(true);

ValueNotifier<bool> isTermsAccepted = ValueNotifier<bool>(false);

Map<int, ValueNotifier<int>> commentCountNotifiers = {};

// ValueNotifier<bool?> twitterNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> vimeoNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> facebookNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> instagramNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> threadNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> linkedInNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> pinterestNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> tumblrNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> redditNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> youtubeNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> tiktokNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> xNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> telegramNotifier = ValueNotifier<bool?>(false);
ValueNotifier<bool?> mastodonNotifier = ValueNotifier<bool?>(false);
// ValueNotifier<bool?> dailymotionNotifier = ValueNotifier<bool?>(false);
// ValueNotifier<bool> scrollTopNotifier = ValueNotifier<bool>(false);
// Permission Roll

ValueNotifier<bool?> isPostPermissionNotifier = ValueNotifier<bool?>(true);
ValueNotifier<bool?> isMassagePermissionNotifier = ValueNotifier<bool?>(true);
ValueNotifier<bool?> isanalyticsPermissionNotifier = ValueNotifier<bool?>(true);
ValueNotifier<bool?> isUserManagementPermissionNotifier = ValueNotifier<bool?>(true);
ValueNotifier<bool?> isBrandManagementPermissionNotifier = ValueNotifier<bool?>(true);
ValueNotifier<bool?> isBlockUnblockPermissionNotifier = ValueNotifier<bool?>(true);
ValueNotifier<bool?> isFeedbackPermissionNotifier = ValueNotifier<bool?>(true);

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
bool isVideo(String url) {
  final videoExtensions = [
    'mp4',
    'mkv',
    'mov',
    'avi',
    'flv',
    'wmv',
    'webm',
    'm4v',
    'mpg',
    'mpeg',
    '3gp',
    '3g2',
    'mts',
    'm2ts',
    'ts',
    'ogv',
    'rm',
    'rmvb',
    'MOV',
  ];
  final extension = url.split('.').last.toLowerCase();
  return videoExtensions.contains(extension);
}

ValueNotifier<String> brandNameNotifier = ValueNotifier("");

ValueNotifier<String> profileImageNotifier = ValueNotifier(
    Prefobj.preferences?.get(Prefkeys.PROFILE) == "" || Prefobj.preferences?.get(Prefkeys.PROFILE) == null
        ? Assets.images.svg.other.svgUserProfile.path
        : Prefobj.preferences?.get(Prefkeys.PROFILE));

Future<File?> getVideoThumbnail(String videoPath) async {
  try {
    final Uint8List? bytes = await VideoThumbnail.thumbnailData(
      video: videoPath,
      imageFormat: ImageFormat.PNG,
      quality: 10,
      maxWidth: 500,
    );

    if (bytes != null) {
      final String tempPath = (await getTemporaryDirectory()).path;
      var uniqueId = const Uuid().v4();
      final File file = File('$tempPath/thumbnail_$uniqueId.jpg');
      await file.writeAsBytes(bytes);
      Logger.lOG("Video Thumbnail :- ${file.path}");
      return file;
    }
  } catch (e) {
    debugPrint("Thumbnail Error: $e");
  }
  return null;
}

final progressNotifier = ValueNotifier<double?>(0.0);
ValueNotifier<bool> scrollTopNotifier = ValueNotifier<bool>(false);
bool isMessageScreen = false;
int massageUserID = 0;
int touser = 0;
final Uuid uuid = Uuid();

String abbreviateNumber(int number) {
  if (number >= 1000000000000) {
    return '${(number / 1000000000000).toStringAsFixed(number % 1000000000000 == 0 ? 0 : 1)}T';
  } else if (number >= 1000000000) {
    return '${(number / 1000000000).toStringAsFixed(number % 1000000000 == 0 ? 0 : 1)}B';
  } else if (number >= 1000000) {
    return '${(number / 1000000).toStringAsFixed(number % 1000000 == 0 ? 0 : 1)}M';
  } else if (number >= 1000) {
    return '${(number / 1000).toStringAsFixed(number % 1000 == 0 ? 0 : 1)}K';
  } else {
    return number.toString();
  }
}

class GlobalSetting {
  static bool isUserMuted = false;
}

void showToastNoPermission({String? message, String? access, ToastificationType? type}) {
  toastification.show(
    type: type ?? ToastificationType.warning,
    showProgressBar: false,
    description: Text(
      message ?? "You don’t have permission to access $access.",
      style: GoogleFonts.montserrat(fontSize: 12.0.sp, fontWeight: FontWeight.w500),
    ),
    autoCloseDuration: const Duration(seconds: 3),
  );
}
