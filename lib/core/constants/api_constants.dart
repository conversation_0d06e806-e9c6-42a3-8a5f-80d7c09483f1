import 'package:flowkar/core/utils/exports.dart';

class APIConfig {
  static String get mainbaseURL => FlavorConfig.instance.env.apiKey;

  static String get baseUrl {
    return FlavorConfig.instance.env.baseUrl;
  }

  static String get liveStreamUrl {
    return 'https://live.flowkar.com';
  }

  static String get policyurl {
    return 'https://flowkar.com/privacy-policy/';
  }

  static String get termsOfUse {
    return 'https://flowkar.com/terms';
  }

  static String get joinSocket {
    return 'join_socket';
  }

  static String get notification {
    return 'notification';
  }

  static String get likePost {
    return 'like_post';
  }

  static String get postcomment {
    return 'post_comment';
  }

  static String get replycomment {
    return 'comment_reply';
  }

  static String get replycommentlike {
    return 'like_reply_comment';
  }

  static String get likecomment {
    return 'like_comment';
  }

  static String get searchuser {
    return 'search_user';
  }

  static String get searchhashtag {
    return 'search_hashtag';
  }

  static String get savedPost {
    return 'save_post';
  }

  static String get sendmessage {
    return 'send_message';
  }

  static String get isTyping {
    return 'is_typing';
  }

  static String get messageRead {
    return 'message_read';
  }

  static String get chatList {
    return 'chat_list';
  }

  static String get receivemessage {
    return 'receive_message';
  }

  // Story
  static String get likeunlikestory {
    return 'like_story';
  }

  static String get followuser {
    return 'follow';
  }

  static String get getUserName {
    return 'get_user_name';
  }

  static String get deleteMessage {
    return 'delete_message';
  }
}
