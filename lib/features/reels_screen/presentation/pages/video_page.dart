// import 'package:flowkar/core/utils/exports.dart';
// import 'package:flowkar/core/utils/loading_animation_widget.dart';
// import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
// import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
// import 'package:flowkar/features/reels_screen/presentation/pages/video_player.dart';
// import 'package:flowkar/features/reels_screen/service/reel_service.dart' as reel_service;
// import 'package:flowkar/features/reels_screen/presentation/widget/reels_shimmer.dart';
// import 'package:flowkar/features/reels_screen/service/reel_service.dart';
// import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
// import 'package:wakelock_plus/wakelock_plus.dart';

// class VideoReelPage extends StatefulWidget {
//   const VideoReelPage({super.key, required this.reelService, required this.index, this.postdata, required this.screen});
//   final reel_service.ReelService reelService;
//   final int index;
//   final PostData? postdata;
//   final String screen;

//   @override
//   State<VideoReelPage> createState() => _VideoReelPageState();
// }

// class _VideoReelPageState extends State<VideoReelPage> with WidgetsBindingObserver {
//   late PageController _pageController;
//   int currentPage = 0;
//   bool _isDisposed = false;
//   int _previousReelCount = 0;

//   @override
//   void initState() {
//     super.initState();

//     if (widget.reelService.reels.isEmpty) {
//       widget.reelService.fetchReels();
//     }
//     WakelockPlus.enable();
//     _pageController = PageController(initialPage: widget.index);
//     currentPage = widget.index;
//     _previousReelCount = widget.reelService.reels.length;

//     widget.reelService.addListener(_onReelServiceChanged);
//     Logger.lOG("widget.postdata ${widget.postdata?.id}");
//     if (widget.postdata != null) {
//       Logger.lOG("Got postdata in VideoReelPage: ${widget.postdata!.toJson()}");

//       final post = widget.postdata!;
//       final alreadyExists = widget.reelService.reels.any((r) => r.id == post.id);

//       if (!alreadyExists) {
//         widget.reelService.reels.insert(
//           0,
//           reel_service.ReelData(
//             id: post.id,
//             title: post.title,
//             description: post.description,
//             location: post.location,
//             likes: post.likes,
//             dislikes: post.dislikes,
//             commentsCount: post.commentsCount,
//             files: post.files,
//             latestComment: '',
//             user: reel_service.ReelUser(
//               userId: post.user.userId,
//               username: post.user.username,
//               name: post.user.name,
//               profileImage: post.user.profileImage,
//             ),
//             isLiked: post.isLiked,
//             isSaved: post.isSaved,
//             createdAt: post.createdAt,
//           ),
//         );
//       }
//     }
//     if (widget.reelService.reels.isEmpty) {
//       widget.reelService.fetchReels();
//     }

//     WidgetsBinding.instance.addObserver(this);
//   }

//   void _onReelServiceChanged() {
//     if (mounted && !_isDisposed) {
//       final currentReelCount = widget.reelService.reels.length;
//       final wasDataAdded = currentReelCount > _previousReelCount;

//       setState(() {
//         _previousReelCount = currentReelCount;
//       });

//       if (wasDataAdded) {
//         Logger.lOG('New reels loaded via pagination. Current page: $currentPage');
//       }
//     }
//   }

//   @override
//   void didChangeAppLifecycleState(AppLifecycleState state) {
//     super.didChangeAppLifecycleState(state);

//     if (_isDisposed) return;

//     switch (state) {
//       case AppLifecycleState.resumed:
//         break;
//       case AppLifecycleState.inactive:
//       case AppLifecycleState.paused:
//       case AppLifecycleState.hidden:
//         WakelockPlus.disable();
//         _pauseAllVideos();
//         break;
//       case AppLifecycleState.detached:
//         WakelockPlus.disable();
//         _cleanupOnExit();
//         break;
//     }
//   }

//   void _pauseAllVideos() {
//     reel_service.GlobalVideoManager().stopAllVideos();
//   }

//   void _cleanupOnExit() {
//     _isDisposed = true;
//     _pauseAllVideos();
//     reel_service.GlobalVideoManager().cleanup();
//     widget.reelService.removeListener(_onReelServiceChanged);
//     _pageController.dispose();
//   }

//   Future<void> _handleRefresh() async {
//     try {
//       await widget.reelService.refreshData();

//       if (_pageController.hasClients && widget.reelService.reels.isNotEmpty) {
//         currentPage = 0;
//         _pageController.animateToPage(
//           0,
//           duration: const Duration(milliseconds: 300),
//           curve: Curves.easeInOut,
//         );
//       }
//     } catch (e) {
//       Logger.lOG('Error during refresh: $e');
//     }
//   }

//   @override
//   void dispose() {
//     _isDisposed = true;
//     WidgetsBinding.instance.removeObserver(this);
//     widget.reelService.removeListener(_onReelServiceChanged);
//     _pageController.dispose();
//     WakelockPlus.disable();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: () async {
//         _cleanupOnExit();
//         switch (widget.screen) {
//           case 'Bottom Nav':
//             NavigatorService.pushReplacement(AppRoutes.bottomNavBar);
//             break;
//           case 'Explore':
//             NavigatorService.goBack();
//             break;
//           case 'Discover':
//             NavigatorService.goBack();
//             break;
//           case 'Profile':
//             NavigatorService.goBack();
//             break;
//           case 'Follower':
//             NavigatorService.goBack();
//             break;
//           case 'PostWidget':
//             NavigatorService.goBack();
//             break;
//           default:
//             NavigatorService.pushReplacement(AppRoutes.bottomNavBar);
//             break;
//         }

//         // NavigatorService.pushReplacement(AppRoutes.bottomNavBar);
//         return true;
//       },
//       child: AnnotatedRegion<SystemUiOverlayStyle>(
//         value: SystemUiOverlayStyle.light.copyWith(
//           statusBarIconBrightness: Brightness.light,
//           systemNavigationBarIconBrightness: Brightness.light,
//         ),
//         child: Scaffold(
//           resizeToAvoidBottomInset: false,
//           extendBodyBehindAppBar: true,
//           backgroundColor: Theme.of(context).customColors.black,
//           appBar: _buildAppBar(context),
//           body: LiquidPullToRefresh(
//             color: Theme.of(context).primaryColor.withOpacity(0.5),
//             showChildOpacityTransition: false,
//             backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
//             onRefresh: _handleRefresh,
//             child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
//               builder: (context, connectivityState) {
//                 if (!connectivityState.isConnected && widget.reelService.reels.isEmpty) {
//                   return ReelShimmer();
//                 } else if (widget.reelService.isLoading && widget.reelService.reels.isEmpty) {
//                   return ReelShimmer();
//                 } else if (widget.reelService.reels.isEmpty) {
//                   return ExceptionWidget(
//                     imagePath: Assets.images.svg.exception.svgNodatafound.path,
//                     showButton: false,
//                     title: Lang.of(context).lbl_no_data_found,
//                     subtitle: Lang.of(context).lbl_no_video_found,
//                   );
//                 }

//                 return ListenableBuilder(
//                   listenable: widget.reelService,
//                   builder: (context, child) {
//                     return PageView.builder(
//                       scrollDirection: Axis.vertical,
//                       controller: _pageController,
//                       itemCount: widget.reelService.reels.length + (widget.reelService.hasMoreData ? 1 : 0),
//                       onPageChanged: (index) {
//                         if (_isDisposed) return;

//                         setState(() {
//                           currentPage = index;
//                         });

//                         widget.reelService.checkAndLoadMore(index);
//                       },
//                       itemBuilder: (context, index) {
//                         if (index == widget.reelService.reels.length) {
//                           return const Center(
//                             child: Column(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 LoadingAnimationWidget(),
//                                 SizedBox(height: 16),
//                                 Text(
//                                   'Loading more reels...',
//                                   style: TextStyle(color: Colors.white),
//                                 ),
//                               ],
//                             ),
//                           );
//                         }

//                         final reel = widget.reelService.reels[index];
//                         if (reel.files.isEmpty) {
//                           return const Center(
//                             child: Text(
//                               'No video available',
//                               style: TextStyle(color: Colors.white),
//                             ),
//                           );
//                         }

//                         return VideoPlayerWidget(
//                           key: Key('reel_${reel.id}_$index'),
//                           reelUrl: reel.files.first,
//                           reelData: reel,
//                           isActive: currentPage == index,
//                         );
//                       },
//                     );
//                   },
//                 );
//               },
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   PreferredSizeWidget _buildAppBar(BuildContext context) {
//     return CustomAppbar(
//       hasLeadingIcon: true,
//       height: 18.h,
//       leading: [
//         InkWell(
//           onTap: () {
//             _cleanupOnExit();
//             switch (widget.screen) {
//               case 'Bottom Nav':
//                 NavigatorService.pushReplacement(AppRoutes.bottomNavBar);
//                 break;
//               case 'Explore':
//                 NavigatorService.goBack();
//                 break;
//               case 'Discover':
//                 NavigatorService.goBack();
//                 break;
//               case 'Follower':
//                 NavigatorService.goBack();
//                 break;
//               case 'PostWidget':
//                 NavigatorService.goBack();
//                 break;
//               case 'Profile':
//                 NavigatorService.goBack();
//                 break;
//               default:
//                 NavigatorService.pushReplacement(AppRoutes.bottomNavBar);
//                 break;
//             }

//             // NavigatorService.pushReplacement(AppRoutes.bottomNavBar);
//           },
//           child: Padding(
//             padding: const EdgeInsets.all(8.0),
//             child: CustomImageView(
//               imagePath: Assets.images.svg.authentication.icBackArrow.path,
//               height: 16.h,
//               color: Theme.of(context).customColors.white,
//             ),
//           ),
//         ),
//         buildSizedBoxW(20.w),
//         Text(
//           'Fleez',
//           style: Theme.of(context)
//               .textTheme
//               .titleLarge
//               ?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp, color: Theme.of(context).customColors.white),
//         ),
//       ],
//     );
//   }
// }

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/reels_screen/presentation/pages/video_player.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart' as reel_service;
import 'package:flowkar/features/reels_screen/presentation/widget/reels_shimmer.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class VideoReelPage extends StatefulWidget {
  const VideoReelPage(
      {super.key,
      required this.reelService,
      required this.index,
      this.postdata,
      this.userId,
      required this.screen,
      this.postDataList});
  final reel_service.ReelService reelService;
  final int index;
  final int? userId;
  final PostData? postdata;
  final List<PostData>? postDataList;
  final String screen;

  @override
  State<VideoReelPage> createState() => _VideoReelPageState();
}

class _VideoReelPageState extends State<VideoReelPage> with WidgetsBindingObserver {
  late PageController _pageController;
  int currentPage = 0;
  bool _isDisposed = false;
  int _previousReelCount = 0;
  bool get isProfileMode => widget.screen == 'Profile';

  @override
  void initState() {
    super.initState();

    Logger.lOG("Video User Profile ${widget.index}  ${widget.postDataList?[widget.index].id}");
    WakelockPlus.enable();
    _pageController = PageController(initialPage: widget.index);
    currentPage = widget.index;

    widget.reelService.addListener(_onReelServiceChanged);
    widget.reelService.reels.clear(); // ✅ Always clear first

    if (widget.postDataList != null && widget.postDataList!.isNotEmpty) {
      final filtered = widget.postDataList!
          .where((post) =>
              post.user.userId.toString() == widget.userId.toString() && post.files.any((file) => isVideo(file)))
          .map((post) => ReelData.fromJson(post.toJson()))
          .toList();

      Logger.lOG("Filtered ${filtered.length} reels for user ${widget.userId}");
      widget.reelService.reels.addAll(filtered);
    } else if (widget.postdata != null) {
      final post = widget.postdata!;
      final alreadyExists = widget.reelService.reels.any((r) => r.id == post.id);

      if (!alreadyExists) {
        widget.reelService.reels.insert(
          0,
          ReelData(
            id: post.id,
            title: post.title,
            description: post.description,
            location: post.location,
            likes: post.likes,
            dislikes: post.dislikes,
            commentsCount: post.commentsCount,
            files: post.files,
            latestComment: '',
            user: ReelUser(
              userId: post.user.userId,
              username: post.user.username,
              name: post.user.name,
              profileImage: post.user.profileImage,
            ),
            isLiked: post.isLiked,
            isSaved: post.isSaved,
            createdAt: post.createdAt,
          ),
        );
      }
    } else if (widget.reelService.reels.isEmpty && widget.userId == null && widget.postdata == null) {
      widget.reelService.fetchReels();
    }

    WidgetsBinding.instance.addObserver(this);
  }

  void _onReelServiceChanged() {
    if (mounted && !_isDisposed) {
      final currentReelCount = widget.reelService.reels.length;
      final wasDataAdded = currentReelCount > _previousReelCount;

      setState(() {
        _previousReelCount = currentReelCount;
      });

      if (wasDataAdded) {
        Logger.lOG('New reels loaded via pagination. Current page: $currentPage');
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (_isDisposed) return;

    switch (state) {
      case AppLifecycleState.resumed:
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
      case AppLifecycleState.hidden:
        WakelockPlus.disable();
        _pauseAllVideos();
        break;
      case AppLifecycleState.detached:
        WakelockPlus.disable();
        _cleanupOnExit();
        break;
    }
  }

  void _pauseAllVideos() {
    reel_service.GlobalVideoManager().stopAllVideos();
  }

  void _cleanupOnExit() {
    _isDisposed = true;
    _pauseAllVideos();
    reel_service.GlobalVideoManager().cleanup();
    widget.reelService.removeListener(_onReelServiceChanged);
    _pageController.dispose();
  }

  Future<void> _handleRefresh() async {
    try {
      await widget.reelService.refreshData();

      if (_pageController.hasClients && widget.reelService.reels.isNotEmpty) {
        currentPage = 0;
        _pageController.animateToPage(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } catch (e) {
      Logger.lOG('Error during refresh: $e');
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    WidgetsBinding.instance.removeObserver(this);
    widget.reelService.removeListener(_onReelServiceChanged);
    _pageController.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _cleanupOnExit();
        NavigatorService.goBack();
        return true;
      },
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.light.copyWith(
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
        child: Scaffold(
            resizeToAvoidBottomInset: false,
            extendBodyBehindAppBar: true,
            backgroundColor: Theme.of(context).customColors.black,
            appBar: _buildAppBar(context),
            body: (widget.screen != "Profile" && widget.screen != 'Follower')
                ? LiquidPullToRefresh(
                    color: Theme.of(context).primaryColor.withOpacity(0.5),
                    showChildOpacityTransition: false,
                    backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                    onRefresh: _handleRefresh,
                    child: reelList(),
                  )
                : reelList()),
      ),
    );
  }

  BlocBuilder<ConnectivityBloc, ConnectivityState> reelList() {
    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, connectivityState) {
        if (!connectivityState.isConnected && widget.reelService.reels.isEmpty) {
          return ReelShimmer();
        } else if (widget.reelService.isLoading && widget.reelService.reels.isEmpty) {
          return ReelShimmer();
        } else if (widget.reelService.reels.isEmpty) {
          return ExceptionWidget(
            imagePath: Assets.images.svg.exception.svgNodatafound.path,
            showButton: false,
            title: Lang.of(context).lbl_no_data_found,
            subtitle: Lang.of(context).lbl_no_video_found,
          );
        }

        return ListenableBuilder(
          listenable: widget.reelService,
          builder: (context, child) {
            return PageView.builder(
              scrollDirection: Axis.vertical,
              controller: _pageController,
              //itemCount: widget.reelService.reels.length + (widget.reelService.hasMoreData ? 1 : 0),
              itemCount: widget.reelService.reels.length +
                  (widget.screen == 'Profile' || widget.screen == 'Follower'
                      ? 0
                      : (widget.reelService.hasMoreData ? 1 : 0)),

              onPageChanged: (index) {
                if (_isDisposed) return;

                setState(() {
                  currentPage = index;
                });

                if (widget.postDataList?.isEmpty ?? false) {
                  widget.reelService.checkAndLoadMore(index);
                }
              },
              itemBuilder: (context, index) {
                if (index == widget.reelService.reels.length) {
                  widget.reelService.checkAndLoadMore(index);
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        LoadingAnimationWidget(),
                        SizedBox(height: 16),
                        Text(
                          'Loading more reels...',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  );
                }

                final reel = widget.reelService.reels[index];
                if (reel.files.isEmpty) {
                  return const Center(
                    child: Text(
                      'No video available',
                      style: TextStyle(color: Colors.white),
                    ),
                  );
                }
                Logger.lOG("${widget.reelService.reels[index].id}");
                return VideoPlayerWidget(
                  key: Key('reel_${reel.id}_$index'),
                  reelUrl: reel.files.first,
                  reelData: reel,
                  isActive: currentPage == index,
                );
              },
            );
          },
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            _cleanupOnExit();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
              color: Theme.of(context).customColors.white,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          'Fleez',
          style: Theme.of(context)
              .textTheme
              .titleLarge
              ?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp, color: Theme.of(context).customColors.white),
        ),
      ],
    );
  }
}
