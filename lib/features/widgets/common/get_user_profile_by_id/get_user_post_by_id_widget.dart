// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class GetUserPostByIdWidget extends StatefulWidget {
  final List<PostData>? userByIdPost;
  final int? initialIndex;
  final int? userId;

  const GetUserPostByIdWidget({super.key, this.userByIdPost, this.initialIndex, this.userId});

  static Widget builder(BuildContext context) {
    return const GetUserPostByIdWidget();
  }

  @override
  State<GetUserPostByIdWidget> createState() => _GetUserPostByIdWidgetState();
}

class _GetUserPostByIdWidgetState extends State<GetUserPostByIdWidget> with SingleTickerProviderStateMixin {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;
  int _initialIndex = 0;
  final int _threshold = 5;
  // Offline message state
  Timer? _offlineMessageTimer;
  bool _showOfflineMessage = false;
  bool _isRefreshAttemptedOffline = false;

  @override
  void initState() {
    super.initState();
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
    _initialIndex = widget.initialIndex ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_initialIndex >= 0 && _initialIndex < (widget.userByIdPost?.length ?? 0)) {
        _scrollToInitialIndex();
      }
    });
    _itemPositionsListener.itemPositions.addListener(_scrollListener);
  }

  void _scrollToInitialIndex() {
    _itemScrollController.jumpTo(
      index: _initialIndex,
    );
  }

  Future<void> _refreshFeed() async {
    final state = context.read<HomeFeedBloc>().state;
    final connectivityState = context.read<ConnectivityBloc>().state;

    if (!connectivityState.isConnected) {
      // User attempted to refresh while offline
      _isRefreshAttemptedOffline = true;
      _offlineMessageTimer?.cancel();
      _offlineMessageTimer = Timer(const Duration(seconds: 5), () {
        if (mounted && _isRefreshAttemptedOffline) {
          setState(() {
            _showOfflineMessage = true;
          });
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showOfflineMessage = false;
                _isRefreshAttemptedOffline = false;
              });
            }
          });
        }
      });
      return;
    }

    if (state.isuserProfileposts.isNotEmpty) {
      state.isuserProfileposts.clear();
    }
    context.read<HomeFeedBloc>().add(GetUserPostApiIdEvent(page: 1, userId: widget.userId));
  }

  void _scrollListener() {
    final visibleItems = _itemPositionsListener.itemPositions.value;
    if (visibleItems.isNotEmpty) {
      final lastVisibleIndex = visibleItems.last.index;

      if (lastVisibleIndex >= (widget.userByIdPost?.length ?? 0) - _threshold) {
        final state = context.read<HomeFeedBloc>().state;
        if (state.getAllUserPostbyidModel?.nextPage == null) {
          return;
        } else {
          if (!state.profilPosteByIdLoadingmore) {
            context
                .read<HomeFeedBloc>()
                .add(GetUserPostApiIdEvent(page: state.userpostbyIdPage + 1, userId: widget.userId));
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Scaffold(
          appBar: CustomAppbar(
            hasLeadingIcon: true,
            height: 18.h,
            leading: [
              InkWell(
                onTap: () {
                  context.read<SurveyBloc>().add(UserHomeDataAPI(context: context));
                  PersistentNavBarNavigator.pop(context);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CustomImageView(
                    imagePath: Assets.images.svg.authentication.icBackArrow.path,
                    height: 16.h,
                  ),
                ),
              ),
              buildSizedBoxW(20.w),
              Text(
                Lang.of(context).lbl_post,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
              ),
            ],
          ),
          body: BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themestate) {
              return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                builder: (context, state) {
                  return Scaffold(
                    body: BlocListener<ConnectivityBloc, ConnectivityState>(
                      listener: (context, state) {
                        if (state.isReconnected) {
                          if (context.read<HomeFeedBloc>().state.isuserProfileposts.isEmpty) {
                            context.read<HomeFeedBloc>().add(GetUserPostApiIdEvent(page: 1, userId: widget.userId));
                          }
                        }
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Expanded(child: _buildUserByIdPost(state, themestate)),
                          BlocBuilder<ConnectivityBloc, ConnectivityState>(
                            builder: (context, connectivityState) {
                              return Visibility(
                                visible: state.profilPosteByIdLoadingmore && connectivityState.isConnected,
                                child: SizedBox(
                                  height: 50.h,
                                  child: Center(
                                      child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
        _buildOfflineMessage(_showOfflineMessage),
      ],
    );
  }

  Widget _buildUserByIdPost(HomeFeedState state, ThemeState themestate) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(
        builder: (context, connectivityState) {
          if (!connectivityState.isConnected && state.isuserProfileposts.isEmpty) {
            return HomeFeedShimmer();
          } else if (state.profilPosteByIdLoading) {
            return HomeFeedShimmer();
          } else if (state.isuserProfileposts.isEmpty) {
            return ListView(
              physics: AlwaysScrollableScrollPhysics(),
              children: [
                buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                ExceptionWidget(
                  imagePath: Assets.images.svg.exception.svgNodatafound.path,
                  showButton: false,
                  title: Lang.of(context).lbl_no_data_found,
                  subtitle: Lang.of(context).lbl_no_post,
                ),
              ],
            );
          } else {
            return ScrollablePositionedList.builder(
              padding: EdgeInsets.zero,
              itemCount: state.isuserProfileposts.length,
              itemScrollController: _itemScrollController,
              itemPositionsListener: _itemPositionsListener,
              physics: const AlwaysScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                final postIndex = index;
                final post = state.isuserProfileposts[postIndex];
                return Padding(
                  padding: EdgeInsets.only(
                      bottom: state.isuserProfileposts[postIndex] == state.isuserProfileposts.last ? 30.h : 0),
                  child: PostWidget(
                    width: post.width,
                    height: post.height,
                    userByIDpost: true,
                    userByIDvideo: false,
                    userVideo: false,
                    userpost: false,
                    state: state,
                    taggedIn: post.taggedIn,
                    latestcomments: post.latestComment.toString(),
                    index: postIndex,
                    userId: post.user.userId,
                    postId: post.id,
                    profileImage: post.user.profileImage,
                    name: post.user.name,
                    username: post.user.username,
                    postMedia: post.files,
                    thumbnailImage: post.thumbnailFiles?.isEmpty ?? true ? [] : post.thumbnailFiles!,
                    title: post.title == "''" || post.title.isEmpty ? '' : post.title,
                    caption:
                        "${post.title == "''" || post.title.isEmpty ? '' : post.title}${post.description == '' || post.description.isEmpty ? '' : post.title == "''" || post.title.isEmpty ? post.description : "\n${post.description}"}",
                    likes: post.likes.toString(),
                    comments: post.commentsCount.toString(),
                    postTime: post.createdAt,
                    isLiked: post.isLiked,
                    isSaved: post.isSaved,
                    screenType: "User Profile",
                    doubleTap: () {
                      if (post.isLiked == false) {
                        context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                      }
                    },
                    likeonTap: () {
                      context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
                    },
                    commentonTap: () {
                      showModalBottomSheet(
                        context: context,
                        useRootNavigator: true,
                        isScrollControlled: true,
                        builder: (context) => CommentsBottomSheet(postId: post.id),
                      );
                      // NavigatorService.pushNamed(AppRoutes.commentBottomSheet, arguments: [post.id]);
                    },
                    shareonTap: () {},
                    saveonTap: () {},
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }

  Widget _buildOfflineMessage(bool showOfflineMessage) {
    return Positioned(
      bottom: 20.h,
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: _showOfflineMessage ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: _showOfflineMessage ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.95),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Theme.of(context).customColors.white,
                  size: 18.sp,
                ),
                buildSizedBoxW(8),
                Text(
                  "Couldn't refresh feed",
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
