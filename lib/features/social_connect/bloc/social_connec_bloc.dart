// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:math';
import 'dart:io';

import 'package:country_picker/country_picker.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/social_connect/model/dynamic_platform_loading_model.dart';
import 'package:flowkar/features/social_connect/model/share_profile_model.dart';
import 'package:flowkar/features/social_connect/model/social_connect_check.dart';
import 'package:flowkar/features/social_connect/model/social_connect_model.dart';
import 'package:flowkar/features/social_connect/model/social_disconect_model.dart';
import 'package:flowkar/features/social_connect/model/telegram_send_code_model.dart';
import 'package:flowkar/features/social_connect/model/telegram_signin_model.dart';
import 'package:flowkar/features/social_connect/model/thread_profile_model.dart';
import 'package:flowkar/features/social_connect/model/thumblr_social_media_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:permission_handler/permission_handler.dart';
part 'social_connec_event.dart';
part 'social_connec_state.dart';

class SocialConnecBloc extends Bloc<SocialConnectEvent, SocialConnectState> {
  static const int initialTime = 60;
  Timer? _timer;
  ApiClient apiClient = ApiClient(Dio());
  SocialConnecBloc()
      : super(SocialConnectState(
          selectedCountry: Country(
            phoneCode: "91",
            countryCode: "IN",
            e164Sc: 91,
            geographic: true,
            level: 1,
            example: "9123456789",
            displayName: "India (IN) +91",
            displayNameNoCountryCode: "India (IN)",
            e164Key: "IN91",
            name: 'India',
          ),
        )) {
    on<LinkdinconnectApiEvent>(_onLinkedinConnectEvent);
    on<LinkdinDisconnectApiEvent>(_onLinkedinDisconnectEvent);
    on<VimeoconnectApiEvent>(_onVimeoConnectEvent);
    on<VimeoDisconnectApiEvent>(_onVimeoDisconnectEvent);
    on<SocialConnectCheckApiEvent>(_onSocialConnectCheckEvent);
    on<PinterstconnectApiEvent>(_onPintrestConnectEvent);
    on<PintrestDisconnectApiEvent>(_onPintrestDisconnectEvent);
    on<RedditconnectApiEvent>(_onRedditConnectEvent);
    on<RedditDisconnectApiEvent>(_onRedditDisconnectEvent);
    on<ConnectTumblrApiEvent>(_connecttumblrApiCall);
    on<SocialconnectTumblrApiEvent>(_socialConnectTumblrApiCall);
    on<TumblrDisconnectApiEvent>(_onTumblrDisconnectEvent);
    on<InstagramconnectApiEvent>(_onInstagramConnectEvent);
    on<InstagramDisconnectApiEvent>(_onInstagramDisconnectEvent);
    on<FacebookconnectApiEvent>(_onFacebookConnectEvent);
    on<FacebookDisconnectApiEvent>(_onFacebookDisconnectEvent);
    on<ThreadconnectApiEvent>(_onThreadConnectEvent);
    on<ThreadDisconnectApiEvent>(_onThreadDisconnectEvent);
    on<YoutubeconnectApiEvent>(_onYoutubeConnectEvent);
    on<YoutubeDisconnectApiEvent>(_onYoutubeDisconnectEvent);
    on<TiktokconnectApiEvent>(_onTiktokConnectEvent);
    on<TiktokDisconnectApiEvent>(_onTiktokDisconnectEvent);
    on<XconnectApiEvent>(_onXConnectEvent);
    on<XDisconnectApiEvent>(_onXDisconnectEvent);
    on<PurchaseXApiEvent>(_onPurcheseXEvent);
    on<ShareProfileApiEvent>(_onShareProfileEvent);
    on<ThreadProfileDataApiEvent>(_oninstaProfileEvent);
    on<DynamicplatformApiEvent>(_ondynamicplatformApi);
    on<CountrySelected>(_onCountrySelected);
    on<PhoneNumberChanged>(_onPhoneNumberChanged);
    on<TelegramconnectSendCodeApiEvent>(_onTelegramSendCodeEvent);
    on<TelegramconnectSignInApiEvent>(_onTelegramSigninAPI);
    on<StartTimerEvent>(_onStartTimer);
    on<TickEvent>(_onTick);
    on<TelegramDisconnectApiEvent>(_onTelegramDisconnectEvent);
    on<MastodonconnectApiEvent>(_onMastodonConnectEvent);
    on<MastodonDisconnectApiEvent>(_onMastodonDisconnectEvent);
    on<ClearTelegramSendCodeModelEvent>((event, emit) {
      _timer?.cancel();
      // emit(state.copyWith(
      //   telegramSendCodeModel: null, // Set to null instead of creating new instance
      //   isResendEnabled: false,
      // ));
    });
    on<ShowPhoneSectionEvent>((event, emit) {
      if (!state.isPhonesection) {
        _timer?.cancel();
      }
      emit(state.copyWith(isPhonesection: event.isPhonesection));
    });
  }

  //============================= Linkedin =============================
  Future<void> _onLinkedinConnectEvent(LinkdinconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isLinkedInConnectLoading: true));
    try {
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';

      final result = await apiClient.connectLinkedin(brandId.toString(), loginuserId);
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isLinkedInConnectLoading: false));
        launchUrlWebview(result.url!, true);
      } else {
        emit(state.copyWith(isLinkedInConnectLoading: false, linkedinConnectError: result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isLinkedInConnectLoading: false, linkedinConnectError: handleError(error)));
    }
  }

  Future<void> _onLinkedinDisconnectEvent(LinkdinDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isLinkedInConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectLinkedin(brandId.toString(), loginuserId);
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isLinkedInConnectLoading: false));
        linkedInNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.LINKEDIN, linkedInNotifier.value);
        updatePlatformStatus('LINKEDIN', linkedInNotifier.value!);
      } else {
        emit(state.copyWith(isLinkedInConnectLoading: false, linkedinConnectError: result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isLinkedInConnectLoading: false, linkedinConnectError: handleError(error)));
    }
  }

//============================= Vimeo =============================
  Future<void> _onVimeoConnectEvent(VimeoconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isVimeoConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectVimeo(brandId.toString(), loginuserId);
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isVimeoConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isVimeoConnectLoading: false, vimeoConnectError: result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isVimeoConnectLoading: false, vimeoConnectError: handleError(error)));
    }
  }

  Future<void> _onVimeoDisconnectEvent(VimeoDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isVimeoConnectLoading: true));
    try {
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.disconnectVimeo(brandId.toString(), loginuserId);
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        vimeoNotifier.value = false;
        Prefobj.preferences?.put(Prefkeys.VIMEO, vimeoNotifier.value) ?? false;
        updatePlatformStatus('VIMEO', vimeoNotifier.value!);
        emit(state.copyWith(socialDisconnectModel: result, isVimeoConnectLoading: false));
      } else {
        emit(state.copyWith(isVimeoConnectLoading: false, vimeoConnectError: result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isVimeoConnectLoading: false, vimeoConnectError: handleError(error)));
    }
  }
// ==================== Launch Url WebView ========================

  Future<void> launchUrlWebview(String url, bool didLaunchUrl) async {
    NavigatorService.goBack();
    final random = Random().nextInt(100000);
    final freshUrl = url.contains('?') ? '$url&cache_bust=$random' : '$url?cache_bust=$random';

    final Uri freshUrlurl = Uri.parse(freshUrl);
    // if (await canLaunchUrl(Uri.parse(freshUrl))) {
    // await launchUrl(Uri.parse(freshUrl));
    final bool response = await launchUrl(freshUrlurl,
        mode: LaunchMode.platformDefault,
        webViewConfiguration: const WebViewConfiguration(
          enableDomStorage: false,
          enableJavaScript: true,
        ));

    if (response) {
      didLaunchUrl = true;
    } else {
      Logger.lOG('Success');
    }
    // } else {
    //   throw Exception('Could not launch $freshUrl');
    // }
  }

  /* Future<void> launchUrlWebview(String launchurl, bool didLaunchUrl) async {
    NavigatorService.goBack();
    final Uri url = Uri.parse(launchurl);

    try {
      final bool response = await launchUrl(url, mode: LaunchMode.platformDefault);

      if (response) {
        didLaunchUrl = true;
      } else {
        Logger.lOG('Success');
      }
    } catch (e) {
      Logger.lOG("error $e");
    }
  }
*/
// ==================== Social Connect Check ========================
  Future<void> _onSocialConnectCheckEvent(SocialConnectCheckApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.socialConnectCheck(brandId.toString(), loginuserId);
      if (result.status == true) {
        // Linkdin
        linkedInNotifier.value = result.data.LinkedIn;
        Prefobj.preferences?.put(Prefkeys.LINKEDIN, linkedInNotifier.value);
        updatePlatformStatus('LINKEDIN', linkedInNotifier.value!);
        // Vimeo
        vimeoNotifier.value = result.data.Vimeo;
        Prefobj.preferences?.put(Prefkeys.VIMEO, vimeoNotifier.value);
        updatePlatformStatus('VIMEO', vimeoNotifier.value!);
        // Pintrest
        pinterestNotifier.value = result.data.Pinterest;
        Prefobj.preferences?.put(Prefkeys.PINTEREST, pinterestNotifier.value);
        updatePlatformStatus('PINTEREST', pinterestNotifier.value!);
        // Redit
        redditNotifier.value = result.data.reddit;
        Prefobj.preferences?.put(Prefkeys.REDDIT, redditNotifier.value);
        updatePlatformStatus('REDDIT', redditNotifier.value!);
        // Instagram
        instagramNotifier.value = result.data.Instagram;
        Prefobj.preferences?.put(Prefkeys.INSTAGRAM, instagramNotifier.value);
        updatePlatformStatus('INSTAGRAM', instagramNotifier.value!);
        // Facebook
        facebookNotifier.value = result.data.Facebook;
        Prefobj.preferences?.put(Prefkeys.FACEBOOK, facebookNotifier.value);
        updatePlatformStatus('FACEBOOK', facebookNotifier.value!);
        // Thread
        threadNotifier.value = result.data.threads;
        Prefobj.preferences?.put(Prefkeys.THREAD, threadNotifier.value);
        updatePlatformStatus('THREAD', threadNotifier.value!);
        // Youtube
        youtubeNotifier.value = result.data.YouTube;
        Prefobj.preferences?.put(Prefkeys.YOUTUBE, youtubeNotifier.value);
        updatePlatformStatus('YOUTUBE', youtubeNotifier.value!);
        // Tiktok
        tiktokNotifier.value = result.data.tiktok;
        Prefobj.preferences?.put(Prefkeys.TIKTOK, tiktokNotifier.value);
        updatePlatformStatus('TIKTOK', tiktokNotifier.value!);
        // X
        xNotifier.value = result.data.x;
        Prefobj.preferences?.put(Prefkeys.X, xNotifier.value);
        updatePlatformStatus('X', xNotifier.value!);
        Logger.lOG("Linkedin ${linkedInNotifier.value},,   Vimeo,${vimeoNotifier.value}");
        telegramNotifier.value = result.data.telegram;
        updatePlatformStatus('TELEGRAM', telegramNotifier.value!);
        await Prefobj.preferences?.put(Prefkeys.TELEGRAM, telegramNotifier.value);
        mastodonNotifier.value = result.data.mastodon;
        updatePlatformStatus('MASTODON', mastodonNotifier.value!);
        await Prefobj.preferences?.put(Prefkeys.MASTODON, mastodonNotifier.value);

        socialPlatformsStatus.value = {
          'VIMEO': result.data.Vimeo,
          'FACEBOOK': result.data.Facebook,
          'INSTAGRAM': result.data.Instagram,
          'THREAD': result.data.threads,
          'LINKEDIN': result.data.LinkedIn,
          'PINTEREST': result.data.Pinterest,
          'TUMBLR': result.data.tumblr,
          'REDDIT': result.data.reddit,
          'YOUTUBE': result.data.YouTube,
          'TIKTOK': result.data.tiktok,
          'X': result.data.x,
          'TELEGRAM': result.data.telegram,
          'MASTODON': result.data.mastodon,
        };

        emit(state.copyWith(socialConnectCheck: result, isConnectLoading: false));
      } else {
        emit(state.copyWith(isConnectLoading: false, socialConnectError: result.message.toString()));
      }
    } catch (error) {
      Logger.lOG("Reeoe $error");
      emit(state.copyWith(isConnectLoading: false, socialConnectError: handleError(error)));
    }
  }

//============================= Pintrst =============================
  Future<void> _onPintrestConnectEvent(PinterstconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isPintrestConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectPintrest(brandId.toString(), loginuserId);
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isPintrestConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isPintrestConnectLoading: false, pintrestConnectError: result.message.toString()));
        Logger.lOG("Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isPintrestConnectLoading: false, pintrestConnectError: handleError(error)));
    }
  }

  Future<void> _onPintrestDisconnectEvent(PintrestDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isPintrestConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectPintrest(brandId.toString(), loginuserId);
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isPintrestConnectLoading: false));
        pinterestNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.PINTEREST, pinterestNotifier.value);
        updatePlatformStatus('PINTEREST', pinterestNotifier.value!);
      } else {
        emit(state.copyWith(isPintrestConnectLoading: false, pintrestConnectError: result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isPintrestConnectLoading: false, pintrestConnectError: handleError(error)));
    }
  }

//============================= Reddit =============================
  Future<void> _onRedditConnectEvent(RedditconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isReditConnectLoading: true));
    try {
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.connectReddit(brandId.toString(), loginuserId);
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isReditConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isReditConnectLoading: false, redditConnectError: result.message.toString()));
        Logger.lOG(result.message.toString());
      }
    } catch (error) {
      emit(state.copyWith(isReditConnectLoading: false, redditConnectError: handleError(error)));
    }
  }

  Future<void> _onRedditDisconnectEvent(RedditDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isReditConnectLoading: true));
    try {
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final result = await apiClient.disconnectReddit(
        loginuserId,
        brandId.toString(),
      );
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isReditConnectLoading: false));
        redditNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.REDDIT, redditNotifier.value);
        updatePlatformStatus('REDDIT', redditNotifier.value!);
      } else {
        emit(state.copyWith(isReditConnectLoading: false, redditConnectError: result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isReditConnectLoading: false, redditConnectError: handleError(error)));
    }
  }

//============================= Tumblr =============================

  Future<void> _connecttumblrApiCall(
    ConnectTumblrApiEvent event,
    Emitter<SocialConnectState> emit,
  ) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.tumblrAfterAuthApi(
          oauthToken: event.oauthtoken,
          brandid: brandId.toString(),
          oauthTokenSecret: event.oauthtokensecret,
          oauthVerifier: event.oauthverifier,
          logInUserId: loginuserId);
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isReditConnectLoading: false));
        tumblrNotifier.value = result.status;
        Prefobj.preferences?.put(Prefkeys.TUMBLR, tumblrNotifier.value);
        updatePlatformStatus('TUMBLR', tumblrNotifier.value!);
        Logger.lOG("tumbler ${Prefobj.preferences?.get(Prefkeys.TUMBLR)}");
      } else {
        emit(state.copyWith(isReditConnectLoading: false, redditConnectError: result.message.toString()));
        Logger.lOG(result.message.toString());
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  Future<void> _socialConnectTumblrApiCall(
    SocialconnectTumblrApiEvent event,
    Emitter<SocialConnectState> emit,
  ) async {
    emit(state.copyWith(isTumblrLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectTumblr(logInUserId: loginuserId, brandid: brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(
          tumblrSocialMediaModel: result,
          isTumblrLoading: false,
        ));
        Prefobj.preferences?.put(Prefkeys.OAUTH_TUMBLER, result.oauthToken);
        Prefobj.preferences?.put(Prefkeys.OAUTH_SECRET_TUMBLER, result.oauthTokenSecret);
        NavigatorService.pushNamed(AppRoutes.socialConnectwebview,
            arguments: [result.authorizationUrl ?? '', 'Tumblr']);
      } else {
        Logger.lOG("Tumbler ${result.status}");
        emit(state.copyWith(isTumblrLoading: false));
      }
    } catch (e) {
      Logger.lOG("Error $e");
      emit(state.copyWith(isTumblrLoading: false));
    }
  }

  Future<void> _onTumblrDisconnectEvent(TumblrDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isTumblrLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectTumblr(logInUserId: loginuserId, brandid: brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isTumblrLoading: false));
        tumblrNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.TUMBLR, tumblrNotifier.value);
        updatePlatformStatus('TUMBLR', tumblrNotifier.value!);
      } else {
        emit(state.copyWith(isTumblrLoading: false, tumblrConnectError: result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isTumblrLoading: false, tumblrConnectError: handleError(error)));
    }
  }

//============================= Instagram =============================
  Future<void> _onInstagramConnectEvent(InstagramconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isInstagramConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectInstagram(loginuserId, brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isInstagramConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isInstagramConnectLoading: false));
        Logger.lOG("Instagram Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isInstagramConnectLoading: false));
    }
  }

  Future<void> _onInstagramDisconnectEvent(InstagramDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isInstagramConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectInstagram(loginuserId, brandId.toString());
      Future.delayed(Duration(seconds: 2));
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        emit(state.copyWith(socialDisconnectModel: result, isInstagramConnectLoading: false));
        instagramNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.INSTAGRAM, instagramNotifier.value);
        updatePlatformStatus('INSTAGRAM', instagramNotifier.value!);
      } else {
        emit(state.copyWith(
          isInstagramConnectLoading: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(isInstagramConnectLoading: false));
    }
  }

//============================= Facebook =============================
  Future<void> _onFacebookConnectEvent(FacebookconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isFacebookConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectFacebook(loginuserId, brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isFacebookConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isFacebookConnectLoading: false));
        Logger.lOG("Facebook Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isFacebookConnectLoading: false));
    }
  }

  Future<void> _onFacebookDisconnectEvent(FacebookDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isFacebookConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectFacebook(loginuserId, brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isFacebookConnectLoading: false));
        facebookNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.FACEBOOK, facebookNotifier.value);
        updatePlatformStatus('FACEBOOK', facebookNotifier.value!);
      } else {
        emit(state.copyWith(
          isFacebookConnectLoading: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(isFacebookConnectLoading: false));
    }
  }

//============================= Thread =============================
  Future<void> _onThreadConnectEvent(ThreadconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isThreadConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectThread(loginuserId, brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isThreadConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isThreadConnectLoading: false));
        Logger.lOG("Thread Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isThreadConnectLoading: false));
    }
  }

  Future<void> _onThreadDisconnectEvent(ThreadDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isThreadConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectThread(loginuserId, brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isThreadConnectLoading: false));
        threadNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.THREAD, threadNotifier.value);
        updatePlatformStatus('THREAD', threadNotifier.value!);
      } else {
        emit(state.copyWith(
          isThreadConnectLoading: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(isThreadConnectLoading: false));
    }
  }

//============================= Youtube =============================
  Future<void> _onYoutubeConnectEvent(YoutubeconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isYoutubeConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectYoutube(loginuserId, brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isYoutubeConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isYoutubeConnectLoading: false));
        Logger.lOG("Youtube Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isYoutubeConnectLoading: false));
    }
  }

  Future<void> _onYoutubeDisconnectEvent(YoutubeDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isYoutubeConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectYoutube(loginuserId, brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isYoutubeConnectLoading: false));
        youtubeNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.YOUTUBE, youtubeNotifier.value);
        updatePlatformStatus('YOUTUBE', youtubeNotifier.value!);
      } else {
        emit(state.copyWith(
          isYoutubeConnectLoading: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(isYoutubeConnectLoading: false));
    }
  }

//============================= Tiktok =============================
  Future<void> _onTiktokConnectEvent(TiktokconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isTiktokConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectTiktok(loginuserId, brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isTiktokConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isTiktokConnectLoading: false));
        Logger.lOG("tiktok Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isTiktokConnectLoading: false));
    }
  }

  Future<void> _onTiktokDisconnectEvent(TiktokDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isTiktokConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectTiktok(loginuserId, brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isTiktokConnectLoading: false));
        tiktokNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.TIKTOK, tiktokNotifier.value);
        updatePlatformStatus('TIKTOK', tiktokNotifier.value!);
      } else {
        emit(state.copyWith(
          isTiktokConnectLoading: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(isTiktokConnectLoading: false));
    }
  }

//============================= X- (Twitter) =============================
  Future<void> _onXConnectEvent(XconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    bool isLoading = false;
    emit(state.copyWith(isXConnectLoading: true));
    // final csState = event.context.read<SocialConnecBloc>().state;
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectX(loginuserId, brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isXConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isXConnectLoading: false));
        Logger.lOG("X Error ${result.message}");
        if (Navigator.canPop(event.context)) {
          Navigator.of(event.context).pop();
        }
        showDialog(
            context: event.context,
            barrierDismissible: isLoading,
            builder: (context) {
              return StatefulBuilder(
                builder: (context, setState) {
                  return CustomAlertDialog(
                    imagePath: Assets.images.icons.social.twitter.path,
                    title: "Please Subscribe to X",
                    subtitle: "",
                    onConfirmButtonPressed: () async {
                      const phoneNumber = "9023371032";

                      final Uri launchUri = Uri(
                        scheme: 'tel',
                        path: phoneNumber,
                      );
                      await launchUrl(launchUri);
                    },
                    confirmButtonText: 'Contect',
                    isLoading: isLoading,
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Please Contect us.",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16.sp,
                                  color: Theme.of(context).iconTheme.color),
                              textAlign: TextAlign.center,
                            ),
                            // Text(
                            //   "\$10 ",
                            //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            //       fontWeight: FontWeight.w400,
                            //       fontSize: 16.sp,
                            //       color: Theme.of(context).iconTheme.color),
                            //   textAlign: TextAlign.center,
                            // ),
                            // Text(
                            //   "\$15 ",
                            //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            //         fontWeight: FontWeight.w400,
                            //         fontSize: 14.sp,
                            //         color: Theme.of(context).iconTheme.color?.withOpacity(0.5),
                            //         decoration: TextDecoration.lineThrough,
                            //       ),
                            //   textAlign: TextAlign.center,
                            // ),
                            // Text(
                            //   "/ Month.",
                            //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            //       fontWeight: FontWeight.w400,
                            //       fontSize: 16.sp,
                            //       color: Theme.of(context).iconTheme.color),
                            //   textAlign: TextAlign.center,
                            // ),
                          ],
                        )
                      ],
                    ),
                  );
                },
              );
            });
      }
    } catch (error) {
      emit(state.copyWith(isXConnectLoading: false));
    }
  }

  Future<void> _onXDisconnectEvent(XDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isXConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectX(loginuserId, brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isXConnectLoading: false));
        tiktokNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.TIKTOK, tiktokNotifier.value);
        updatePlatformStatus('TIKTOK', tiktokNotifier.value!);
      } else {
        emit(state.copyWith(isXConnectLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isXConnectLoading: false));
    }
  }

  Future<void> _onPurcheseXEvent(PurchaseXApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isPurchaseXLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.purchaseX(logInUserId: loginuserId, brandid: brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(applaunchurL: true, isPurchaseXLoading: false));
        launchUrlWebview(result.data!, true);
      } else {
        emit(state.copyWith(isPurchaseXLoading: false));
        Logger.lOG("purchase X Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isPurchaseXLoading: false));
    }
  }

  // ==================== Share Profile ========================
  Future<void> _onShareProfileEvent(ShareProfileApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isShareProfileLoading: true));

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getShareProfile(loginuserId, brandId.toString());

      if (result.status == true) {
        // Extract social links from response
        final List<SocialLink> socialLinks = result.profile.socialLinks;

        // Get current platform status map
        Map<String, bool> updatedPlatformStatuses = Map<String, bool>.from(socialPlatformsStatus.value);

        for (var link in socialLinks) {
          final platform = link.platform.toUpperCase();
          final userStatus = link.userStatus ?? false;

          // Check if platform exists in socialPlatformsStatus before updating
          if (updatedPlatformStatuses.containsKey(platform)) {
            updatedPlatformStatuses[platform] = userStatus;
            await Prefobj.preferences?.put(platform, userStatus);
          }
        }

        VibrationHelper.singleShortBuzz();

        // Set values to notifiers
        vimeoNotifier.value = updatedPlatformStatuses['VIMEO'] ?? false;
        facebookNotifier.value = updatedPlatformStatuses['FACEBOOK'] ?? false;
        instagramNotifier.value = updatedPlatformStatuses['INSTAGRAM'] ?? false;
        threadNotifier.value = updatedPlatformStatuses['THREAD'] ?? false;
        linkedInNotifier.value = updatedPlatformStatuses['LINKEDIN'] ?? false;
        pinterestNotifier.value = updatedPlatformStatuses['PINTEREST'] ?? false;
        tumblrNotifier.value = updatedPlatformStatuses['TUMBLR'] ?? false;
        redditNotifier.value = updatedPlatformStatuses['REDDIT'] ?? false;
        youtubeNotifier.value = updatedPlatformStatuses['YOUTUBE'] ?? false;
        tiktokNotifier.value = updatedPlatformStatuses['TIKTOK'] ?? false;
        xNotifier.value = updatedPlatformStatuses["X"] ?? false;
        telegramNotifier.value = updatedPlatformStatuses['TELEGRAM'] ?? false;
        mastodonNotifier.value = updatedPlatformStatuses['MASTODON'] ?? false;
        socialPlatformsStatus.value = {
          'VIMEO': updatedPlatformStatuses['VIMEO'] ?? false,
          'FACEBOOK': updatedPlatformStatuses['FACEBOOK'] ?? false,
          'INSTAGRAM': updatedPlatformStatuses['INSTAGRAM'] ?? false,
          'THREAD': updatedPlatformStatuses['THREAD'] ?? false,
          'LINKEDIN': updatedPlatformStatuses['LINKEDIN'] ?? false,
          'PINTEREST': updatedPlatformStatuses['PINTEREST'] ?? false,
          'TUMBLR': updatedPlatformStatuses['TUMBLR'] ?? false,
          'REDDIT': updatedPlatformStatuses['REDDIT'] ?? false,
          'YOUTUBE': updatedPlatformStatuses['YOUTUBE'] ?? false,
          'TIKTOK': updatedPlatformStatuses['TIKTOK'] ?? false,
          'X': updatedPlatformStatuses["X"] ?? false,
          'TELEGRAM': updatedPlatformStatuses['TELEGRAM'] ?? false,
          'MASTODON': updatedPlatformStatuses['MASTODON'] ?? false,
        };
        // Update ValueNotifier with the new map
        // socialPlatformsStatus.value = updatedPlatformStatuses;
        // Logger.lOG("Updated socialPlatformsStatus: ${socialPlatformsStatus.value}");

        emit(state.copyWith(shareProfileModel: result, isShareProfileLoading: false));
      } else {
        emit(state.copyWith(isShareProfileLoading: false));
      }
    } catch (error) {
      Logger.lOG("Error: $error");
      emit(state.copyWith(isShareProfileLoading: false, socialConnectError: handleError(error)));
    }
  }
  // Future<void> _onShareProfileEvent(ShareProfileApiEvent event, Emitter<SocialConnectState> emit) async {
  //   emit(state.copyWith(isShareProfileLoading: true));

  //   try {
  //     int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
  //     final result = await apiClient.getShareProfile(brandId.toString());
  //     if (result.status == true) {
  //       // Extract social links from response
  //       final List<SocialLink> socialLinks = result.profile.socialLinks ?? [];

  //       // Define a map to store platform statuses
  //       Map<String, bool> platformStatuses = {};
  //       Map<String, bool> userStatuses = {};

  //       for (var link in socialLinks) {
  //         final platform = link.platform.toUpperCase();
  //         final userStatus = link.userStatus ?? false;

  //         platformStatuses[platform] = userStatus;
  //         userStatuses[platform] = userStatus;

  //         updatePlatformStatus(platform, userStatus);
  //         Prefobj.preferences?.put(platform, userStatus);
  //       }

  //       // Set values to notifiers
  //       vimeoNotifier.value = userStatuses['VIMEO'] ?? false;
  //       facebookNotifier.value = userStatuses['FACEBOOK'] ?? false;
  //       instagramNotifier.value = userStatuses['INSTAGRAM'] ?? false;
  //       threadNotifier.value = userStatuses['THREAD'] ?? false;
  //       linkedInNotifier.value = userStatuses['LINKEDIN'] ?? false;
  //       pinterestNotifier.value = userStatuses['PINTEREST'] ?? false;
  //       tumblrNotifier.value = userStatuses['TUMBLR'] ?? false;
  //       redditNotifier.value = userStatuses['REDDIT'] ?? false;
  //       youtubeNotifier.value = userStatuses['YOUTUBE'] ?? false;
  //       tiktokNotifier.value = userStatuses['TIKTOK'] ?? false;

  //       socialPlatformsStatus.value = platformStatuses;
  //       Logger.lOG(socialPlatformsStatus.value);

  //       emit(state.copyWith(shareProfileModel: result, isShareProfileLoading: false));
  //     } else {
  //       emit(state.copyWith(isShareProfileLoading: false));
  //     }
  //   } catch (error) {
  //     Logger.lOG("Error: $error");
  //     emit(state.copyWith(isShareProfileLoading: false, socialConnectError: handleError(error)));
  //   }
  // }

  // ==================== Insta Profile  Data========================
  Future<void> _oninstaProfileEvent(ThreadProfileDataApiEvent event, Emitter<SocialConnectState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      String brandId = Prefobj.preferences?.get(Prefkeys.BRANDID) ?? '';
      emit(state.copyWith(getanalyticsLoading: true));
      final result = await apiClient.getThreadProfileData(logInUserId: loginuserId, brandid: brandId);
      if (result.status == true) {
        Logger.lOG(result.data?.name.toString());
        final profileData = result.data;
        if (profileData != null) {
          emit(state.copyWith(
            instaProfileModel: result,
            getanalyticsLoading: false,
          ));
        } else {
          emit(state.copyWith(getanalyticsLoading: false));
          Logger.lOG('Profile data is null');
        }
      } else {
        emit(state.copyWith(getanalyticsLoading: false));
        Logger.lOG('Result status is false');
      }
    } catch (error) {
      Logger.lOG("Reeoe $error");
      emit(state.copyWith(getanalyticsLoading: false));
      Logger.lOG("Error occurred: $error");
      // emit(state.copyWith(socialConnectError: handleError(error)));
    }
  }

  Future _ondynamicplatformApi(DynamicplatformApiEvent event, Emitter emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.dynamicplatformLoading(loginuserId, brandId.toString());
      if (result.status == true) {
        if (result.data != null) {
          emit(state.copyWith(platformstatus: result.data?.platformStatus, userstatus: result.data?.userStatus));
        } else {
          Logger.lOG('result.data is null');
        }
      } else {
        Logger.lOG('Result status is false');
      }
    } catch (error) {
      Logger.lOG("Error $error");
      Logger.lOG("Error occurred: $error");
    }
  }

  void _onCountrySelected(CountrySelected event, Emitter emit) {
    emit(state.copyWith(selectedCountry: event.country));
  }

  void _onPhoneNumberChanged(PhoneNumberChanged event, Emitter emit) {
    emit(state.copyWith(phoneNumber: event.phoneNumber));
  }

  //============================= Telegram Connect===========================================
  Future<void> _onTelegramSendCodeEvent(TelegramconnectSendCodeApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isTelegramSendCodeLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectTelegramSendCode(loginuserId, brandId.toString(), event.mobileNumber);

      if (result.status == true) {
        // Start the timer when OTP is sent successfully
        _onStartTimer(StartTimerEvent(), emit);

        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          description: Text(
            maxLines: 2,
            "${result.message}. Please check your telegram.",
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 4),
        );

        emit(state.copyWith(telegramSendCodeModel: result, isTelegramSendCodeLoading: false, isPhonesection: true));
      } else {
        emit(state.copyWith(isTelegramSendCodeLoading: false));
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          description: Text(
            maxLines: 2,
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 4),
        );
        Logger.lOG("Telegram Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isTelegramSendCodeLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                )
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onTelegramSigninAPI(TelegramconnectSignInApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isTelegramSignInLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectTelegramSignIn(
          loginuserId, brandId.toString(), event.mobileNumber, event.code, event.phoneCodeHash);

      if (result.status == true) {
        // Cancel timer when successfully signed in
        _timer?.cancel();
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);

        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          description: Text(
            maxLines: 2,
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 4),
        );

        // Store telegram connection status
        Prefobj.preferences?.put(Prefkeys.TELEGRAM, true);

        emit(state.copyWith(
          telegramSignInModel: result,
          isTelegramSignInLoading: false,
        ));

        // Pop the bottom sheet
        Navigator.of(event.context).pop();
      } else {
        emit(state.copyWith(isTelegramSignInLoading: false));
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          description: Text(
            maxLines: 2,
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 4),
        );
        Logger.lOG("Telegram SignIn Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isTelegramSignInLoading: false));
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                )
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  void _onStartTimer(StartTimerEvent event, Emitter<SocialConnectState> emit) {
    emit(state.copyWith(remainingTime: initialTime, isResendEnabled: false));

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (timer.tick >= initialTime) {
        add(TickEvent(0));
        timer.cancel();
      } else {
        add(TickEvent(initialTime - timer.tick));
      }
    });
  }

  void _onTick(TickEvent event, Emitter<SocialConnectState> emit) {
    emit(state.copyWith(remainingTime: event.remainingTime, isResendEnabled: event.remainingTime == 0));
  }

  Future<void> _onTelegramDisconnectEvent(TelegramDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isTelegramSignInLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectTelegram(loginuserId, brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(
          socialDisconnectModel: result,
          isTelegramSignInLoading: false,
          telegramSendCodeModel: null,
          isResendEnabled: false,
          remainingTime: 0,
        ));
        telegramNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.TELEGRAM, telegramNotifier.value);
        updatePlatformStatus('TIKTOK', tiktokNotifier.value!);
      } else {
        emit(state.copyWith(isTelegramSignInLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isTelegramSignInLoading: false));
    }
  }

  //============================= Mastodon =============================
  Future<void> _onMastodonConnectEvent(MastodonconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isMastodonConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.connectMastodon(logInUserId: loginuserId, brandid: brandId.toString());
      if (result.status == true) {
        emit(state.copyWith(socialConnectModel: result, applaunchurL: true, isMastodonConnectLoading: false));
        launchUrlWebview(result.url!, event.didLaunchUrl);
      } else {
        emit(state.copyWith(isMastodonConnectLoading: false));
        Logger.lOG("Thread Error ${result.message}");
      }
    } catch (error) {
      emit(state.copyWith(isMastodonConnectLoading: false));
    }
  }

  Future<void> _onMastodonDisconnectEvent(MastodonDisconnectApiEvent event, Emitter<SocialConnectState> emit) async {
    emit(state.copyWith(isMastodonConnectLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.disconnectMastodon(loginuserId, brandId.toString());
      if (result.status == true) {
        await _onShareProfileEvent(ShareProfileApiEvent(), emit);
        Future.delayed(Duration(seconds: 2));
        emit(state.copyWith(socialDisconnectModel: result, isMastodonConnectLoading: false));
        mastodonNotifier.value = result.connect;
        Prefobj.preferences?.put(Prefkeys.MASTODON, mastodonNotifier.value);
        updatePlatformStatus('MASTODON', threadNotifier.value!);
      } else {
        emit(state.copyWith(
          isMastodonConnectLoading: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(isMastodonConnectLoading: false));
    }
  }
}
