import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flowkar/features/profile_screen/model/tag_post_list_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class GetProfiletagPostWidget extends StatefulWidget {
  final List<TagPostData>? profilepost;
  final int? initialIndex;
  final int? userId;

  const GetProfiletagPostWidget({super.key, this.profilepost, this.initialIndex, this.userId});

  static Widget builder(BuildContext context) {
    return const GetProfiletagPostWidget();
  }

  @override
  State<GetProfiletagPostWidget> createState() => _GetProfiletagPostWidgetState();
}

class _GetProfiletagPostWidgetState extends State<GetProfiletagPostWidget> with SingleTickerProviderStateMixin {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;
  int _initialIndex = 0;
  // final int _threshold = 5;
  final Map<int, Widget> _postCache = {};
  bool isLoadingMore = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
    _initialIndex = widget.initialIndex ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_initialIndex >= 0 && _initialIndex < (widget.profilepost?.length ?? 0)) {
        _scrollToInitialIndex();
      }
    });
    // _itemPositionsListener.itemPositions.addListener(_scrollListener);
  }

  void _scrollToInitialIndex() {
    _itemScrollController.jumpTo(
      index: _initialIndex,
    );
  }

  Future<void> _refreshFeed() async {
    if (!mounted) return;

    final state = context.read<HomeFeedBloc>().state;
    if (state.tagPostData.isNotEmpty) {
      state.tagPostData.clear();
    }
    _postCache.clear();
    context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1));
  }

  // void _scrollListener() {
  //   if (_debounceTimer?.isActive ?? false) return;

  //   _debounceTimer = Timer(const Duration(milliseconds: 200), () {
  //     if (!mounted) return;

  //     final visibleItems = _itemPositionsListener.itemPositions.value;
  //     if (visibleItems.isEmpty) return;

  //     final lastVisibleIndex = visibleItems.last.index;
  //     if (lastVisibleIndex >= (widget.profilepost?.length ?? 0) - _threshold) {
  //       final state = context.read<HomeFeedBloc>().state;
  //       if (state.tagPostmodel?.next == null || _isLoadingMore) {
  //         return;
  //       }

  //       _isLoadingMore = true;
  //       context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: state.tagpage + 1));
  //     }
  //   });
  // }

  Widget _buildPostItem(TagPostData post, int index) {
    if (_postCache.containsKey(post.id)) {
      return _postCache[post.id]!;
    }

    final widget = RepaintBoundary(
      child: PostWidget(
        key: ValueKey('tag_post_${post.id}'),
        width: post.width ?? 0,
        height: post.height ?? 0,
        userByIDpost: false,
        userByIDvideo: false,
        userVideo: false,
        userpost: false,
        state: context.read<HomeFeedBloc>().state,
        latestcomments: post.latestComment.toString(),
        index: index,
        userId: post.user?.userId,
        postId: post.id ?? 0,
        profileImage: "${post.user?.profileImage}",
        name: "${post.user?.name}",
        username: "${post.user?.username}",
        postMedia: post.files ?? [],
        thumbnailImage: [],
        title: "${post.title == "''" || post.title!.isEmpty ? '' : post.title}",
        caption:
            "${post.title == "''" || post.title!.isEmpty ? '' : post.title}${post.description == '' || post.description!.isEmpty ? '' : post.title == "''" || post.title!.isEmpty ? post.description : "\n${post.description ?? ''}"}",
        likes: post.likes.toString(),
        comments: post.commentsCount.toString(),
        postTime: "${post.createdAt}",
        isLiked: post.isLiked ?? false,
        isSaved: post.isSaved ?? false,
        screenType: "User Profile",
        taggedIn: post.taggedIn ?? false,
        doubleTap: () {
          if (post.isLiked == false) {
            context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id ?? 0));
          }
        },
        likeonTap: () {
          context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id ?? 0));
        },
        commentonTap: () {
          showModalBottomSheet(
            context: context,
            useRootNavigator: true,
            isScrollControlled: true,
            builder: (context) => CommentsBottomSheet(postId: post.id ?? 0),
          );
        },
        shareonTap: () {},
        saveonTap: () {},
      ),
    );

    _postCache[post.id ?? 0] = widget;
    return widget;
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    // _itemPositionsListener.itemPositions.removeListener(_scrollListener);
    _postCache.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        hasLeadingIcon: true,
        height: 18.h,
        leading: [
          InkWell(
            onTap: () {
              PersistentNavBarNavigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: CustomImageView(
                imagePath: Assets.images.svg.authentication.icBackArrow.path,
                height: 16.h,
              ),
            ),
          ),
          buildSizedBoxW(20.w),
          Text(
            'Tag Posts',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
          ),
        ],
      ),
      body: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themestate) {
          return BlocBuilder<HomeFeedBloc, HomeFeedState>(
            builder: (context, state) {
              if (state.getprofilrPosteLoading) {
                return HomeFeedShimmer();
              }
              return Scaffold(
                body: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(child: _buildprofilepost(state, themestate)),
                    BlocBuilder<ConnectivityBloc, ConnectivityState>(
                      builder: (context, connectivityState) {
                        return Visibility(
                          visible: state.getprofilPosteLoadingmore && connectivityState.isConnected,
                          child: SizedBox(
                            height: 50.h,
                            child:
                                Center(child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildprofilepost(HomeFeedState state, ThemeState themestate) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: state.isdiscoverloding
          ? LoadingAnimationWidget()
          : NotificationListener<ScrollUpdateNotification>(
              onNotification: (notification) {
                if (notification.metrics.pixels == notification.metrics.maxScrollExtent) {
                  // _handlePagination();
                  // Handle tag post pagination
                  if (state.tagPostmodel?.next != null && !state.istagLoadingMore) {
                    isLoadingMore = true;
                    context.read<HomeFeedBloc>().add(GetTagPostApi(tagpage: state.tagpage + 1));
                  }
                }
                return true;
              },
              child: ScrollablePositionedList.builder(
                padding: EdgeInsets.zero,
                itemCount: state.tagPostData.length,
                itemScrollController: _itemScrollController,
                itemPositionsListener: _itemPositionsListener,
                physics: const ClampingScrollPhysics(),
                itemBuilder: (context, index) {
                  final post = state.tagPostData[index];
                  return Padding(
                    padding: EdgeInsets.only(
                        bottom: state.tagPostData[index] == state.tagPostData.last ? 30.h : 0,
                        top: index == 0 ? 8.h : 0),
                    child: _buildPostItem(post, index),
                  );
                },
              ),
            ),
    );
  }
}
