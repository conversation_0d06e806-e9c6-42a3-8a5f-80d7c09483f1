// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/comment_bottom_sheet_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class GetProfilePostWidget extends StatefulWidget {
  final List<PostData>? profilepost;
  final int? initialIndex;
  final int? userId;

  const GetProfilePostWidget({super.key, this.profilepost, this.initialIndex, this.userId});

  static Widget builder(BuildContext context) {
    return const GetProfilePostWidget();
  }

  @override
  State<GetProfilePostWidget> createState() => _GetProfilePostWidgetState();
}

class _GetProfilePostWidgetState extends State<GetProfilePostWidget> with SingleTickerProviderStateMixin {
  late ItemScrollController _itemScrollController;
  late ItemPositionsListener _itemPositionsListener;
  int _initialIndex = 0;
  final Map<int, Widget> _postCache = {};
  bool isLoadingMore = false;
  Timer? _debounceTimer;
  // Offline message state
  Timer? _offlineMessageTimer;
  bool _showOfflineMessage = false;
  bool _isRefreshAttemptedOffline = false;

  // Add these variables for video control
  int? _currentVisibleVideoIndex;
  bool _isScrolling = false;
  Timer? _scrollTimer;

  @override
  void initState() {
    super.initState();
    _itemScrollController = ItemScrollController();
    _itemPositionsListener = ItemPositionsListener.create();
    _initialIndex = widget.initialIndex ?? 0;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_initialIndex >= 0 && _initialIndex < (widget.profilepost?.length ?? 0)) {
        _scrollToInitialIndex();
      }
    });

    // Add scroll listener for video control
    _itemPositionsListener.itemPositions.addListener(_onScrollChanged);
  }

  void _scrollToInitialIndex() {
    _itemScrollController.jumpTo(
      index: _initialIndex,
    );
  }

  Future<void> _refreshFeed() async {
    if (!mounted) return;

    final state = context.read<HomeFeedBloc>().state;
    final connectivityState = context.read<ConnectivityBloc>().state;

    if (!connectivityState.isConnected) {
      // User attempted to refresh while offline
      _isRefreshAttemptedOffline = true;
      _offlineMessageTimer?.cancel();
      _offlineMessageTimer = Timer(const Duration(seconds: 5), () {
        if (mounted && _isRefreshAttemptedOffline) {
          setState(() {
            _showOfflineMessage = true;
          });
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showOfflineMessage = false;
                _isRefreshAttemptedOffline = false;
              });
            }
          });
        }
      });
      return;
    }

    if (state.getProfilePost.isNotEmpty) {
      state.getProfilePost.clear();
    }
    _postCache.clear();
    context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1));
  }

  // void _scrollListener() {
  //   if (_debounceTimer?.isActive ?? false) return;

  //   _debounceTimer = Timer(const Duration(milliseconds: 200), () {
  //     if (!mounted) return;

  //     final visibleItems = _itemPositionsListener.itemPositions.value;
  //     if (visibleItems.isEmpty) return;

  //     final lastVisibleIndex = visibleItems.last.index;
  //     if (lastVisibleIndex >= (widget.profilepost?.length ?? 0) - _threshold) {
  //       final state = context.read<HomeFeedBloc>().state;
  //       if (state.getProfilePostmodel?.nextPage == null || _isLoadingMore) {
  //         return;
  //       }

  //       _isLoadingMore = true;
  //       context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: state.getProfilepostpage + 1));
  //     }
  //   });
  // }

  void _onScrollChanged() {
    if (!mounted) return;

    // Set scrolling state
    _isScrolling = true;

    // Cancel previous timer
    _scrollTimer?.cancel();

    // Set timer to detect when scrolling stops
    _scrollTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _isScrolling = false;
        });
        _updateVisibleVideo();
      }
    });

    // Pause videos while scrolling
    if (_isScrolling) {
      _pauseAllVideos();
    }
  }

  void _updateVisibleVideo() {
    if (!mounted) return;

    final visibleItems = _itemPositionsListener.itemPositions.value;
    if (visibleItems.isEmpty) return;

    // Find the most visible video item
    int? mostVisibleVideoIndex;
    double maxVisibleArea = 0;

    for (final item in visibleItems) {
      final index = item.index;
      if (index < (widget.profilepost?.length ?? 0)) {
        final post = widget.profilepost![index];
        final isVideoPost = post.files.isNotEmpty && isVideo(post.files.first);

        if (isVideoPost) {
          final visibleArea = (item.itemTrailingEdge - item.itemLeadingEdge) *
              (1 - item.itemLeadingEdge.abs()) *
              (1 - item.itemTrailingEdge.abs());

          if (visibleArea > maxVisibleArea && visibleArea > 0.5) {
            maxVisibleArea = visibleArea;
            mostVisibleVideoIndex = index;
          }
        }
      }
    }

    // Update current visible video
    if (mostVisibleVideoIndex != _currentVisibleVideoIndex) {
      _currentVisibleVideoIndex = mostVisibleVideoIndex;
      setState(() {});
    }
  }

  void _pauseAllVideos() {
    // This will be handled by the PostWidget based on the _isScrolling state
    setState(() {});
  }

  Widget _buildPostItem(PostData post, int index) {
    final state = context.read<HomeFeedBloc>().state;
    final isVideoPost = post.files.isNotEmpty && isVideo(post.files.first);

    // Determine if this video should auto-play
    final shouldAutoPlay = isVideoPost && !_isScrolling && _currentVisibleVideoIndex == index;

    // Helper function to extract PostWidget from cached RepaintBoundary
    PostWidget? getCachedPostWidget(Widget? cached) {
      if (cached is RepaintBoundary && cached.child is PostWidget) {
        return cached.child as PostWidget;
      }
      return null;
    }

    // Check if we need to invalidate the cache
    final cachedPostWidget = getCachedPostWidget(_postCache[post.id]);
    final shouldInvalidateCache = _postCache.containsKey(post.id) &&
        (cachedPostWidget?.key != ValueKey('post_${post.id}') ||
            cachedPostWidget?.isLiked != post.isLiked ||
            cachedPostWidget?.isSaved != post.isSaved ||
            cachedPostWidget?.title != post.title ||
            cachedPostWidget?.caption != post.description);

    Widget? cachedWidget;

    // For video posts, don't cache to ensure proper playback control
    if (isVideoPost || !_postCache.containsKey(post.id) || shouldInvalidateCache) {
      // Create and cache the core widget
      cachedWidget = RepaintBoundary(
        child: PostWidget(
          key: ValueKey('post_${post.id}_${shouldAutoPlay}'), // Include autoplay state in key
          width: post.width,
          height: post.height,
          userpost: true,
          userByIDpost: false,

          userByIDvideo: isVideoPost,
          userVideo: isVideoPost,
          isPost: !isVideoPost,
          state: state,
          latestcomments: post.latestComment.toString(),
          index: index,
          userId: post.user.userId,
          postId: post.id,
          profileImage: post.user.profileImage,
          name: post.user.name,
          username: post.user.username,
          postMedia: post.files,

          thumbnailImage: post.thumbnailFiles ?? [],
          taggedIn: post.taggedIn,
          title: post.title == "''" || post.title.isEmpty ? '' : post.title,
          caption:
              "${post.title == "''" || post.title.isEmpty ? '' : post.title}${post.description == '' || post.description.isEmpty ? '' : post.title == "''" || post.title.isEmpty ? post.description : "\n${post.description}"}",
          likes: post.likes.toString(),
          comments: post.commentsCount.toString(),
          postTime: post.createdAt,
          isLiked: post.isLiked,
          isSaved: post.isSaved,
          screenType: "User Profile",
          reelScreenType: 'Profile',
          isprofilrpostDelete: true,
          // Control auto-play based on scroll state and visibility
          // autoPlay: shouldAutoPlay,
          // pauseVideo: _isScrolling || _currentVisibleVideoIndex != index,
          doubleTap: () {
            if (post.isLiked == false) {
              context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
            }
          },
          likeonTap: () {
            context.read<HomeFeedBloc>().add(LikePostSocketEvent(postId: post.id));
          },
          commentonTap: () {
            showModalBottomSheet(
              context: context,
              useRootNavigator: true,
              isScrollControlled: true,
              builder: (context) => CommentsBottomSheet(postId: post.id),
            );
          },
          shareonTap: () {},
          saveonTap: () {},
        ),
      );

      // Only cache non-video posts to avoid playback issues
      if (!isVideoPost) {
        _postCache[post.id] = cachedWidget;
      }
    } else {
      cachedWidget = _postCache[post.id];
    }

    return cachedWidget!;
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();

    _scrollTimer?.cancel();
    _offlineMessageTimer?.cancel();
    _itemPositionsListener.itemPositions.removeListener(_onScrollChanged);
    _postCache.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        BlocListener<ConnectivityBloc, ConnectivityState>(
          listener: (context, state) {
            if (state.isReconnected) {
              if (context.read<HomeFeedBloc>().state.getProfilePost.isEmpty) {
                context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1));
              }
            }
          },
          child: Scaffold(
            appBar: CustomAppbar(
              hasLeadingIcon: true,
              height: 18.h,
              leading: [
                InkWell(
                  onTap: () {
                    PersistentNavBarNavigator.pop(context);
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CustomImageView(
                      imagePath: Assets.images.svg.authentication.icBackArrow.path,
                      height: 16.h,
                    ),
                  ),
                ),
                buildSizedBoxW(20.w),
                Text(
                  Lang.of(context).lbl_post,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
                ),
              ],
            ),
            body: BlocBuilder<ThemeBloc, ThemeState>(
              builder: (context, themestate) {
                return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                  builder: (context, state) {
                    return Scaffold(
                      body: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Expanded(child: _buildprofilepost(state, themestate)),
                          BlocBuilder<ConnectivityBloc, ConnectivityState>(
                            builder: (context, connectivityState) {
                              return Visibility(
                                visible: state.getprofilPosteLoadingmore && connectivityState.isConnected,
                                child: SizedBox(
                                  height: 50.h,
                                  child: Center(
                                      child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
        _buildOfflineMessage(_showOfflineMessage),
      ],
    );
  }

  Widget _buildprofilepost(HomeFeedState state, ThemeState themestate) {
    return LiquidPullToRefresh(
      color: Theme.of(context).primaryColor.withOpacity(0.5),
      showChildOpacityTransition: false,
      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      onRefresh: _refreshFeed,
      child: BlocBuilder<ConnectivityBloc, ConnectivityState>(builder: (context, connectivityState) {
        if (!connectivityState.isConnected && state.getProfilePost.isEmpty) {
          return HomeFeedShimmer();
        }
        if (state.getprofilrPosteLoading) {
          return HomeFeedShimmer();
        }
        if (state.getProfilePost.isEmpty) {
          return ListView(
            physics: AlwaysScrollableScrollPhysics(),
            children: [
              buildSizedBoxH(MediaQuery.of(context).size.height / 6),
              ExceptionWidget(
                imagePath: Assets.images.svg.exception.svgNodatafound.path,
                showButton: false,
                title: Lang.of(context).lbl_no_data_found,
                subtitle: Lang.of(context).lbl_no_post,
              ),
            ],
          );
        }
        return NotificationListener<ScrollUpdateNotification>(
          onNotification: (notification) {
            if (notification.metrics.pixels == notification.metrics.maxScrollExtent) {
              // _handlePagination();
              // Handle text post pagination
              if (state.getProfilePostmodel?.nextPage != null && !state.getprofilPosteLoadingmore) {
                isLoadingMore = true;
                context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: state.getProfilepostpage + 1));
              }
            }
            return true;
          },
          child: ScrollablePositionedList.builder(
            padding: EdgeInsets.zero,
            itemCount: state.getProfilePost.length,
            itemScrollController: _itemScrollController,
            itemPositionsListener: _itemPositionsListener,
            physics: const ClampingScrollPhysics(),
            itemBuilder: (context, index) {
              final post = state.getProfilePost[index];
              return Padding(
                padding: EdgeInsets.only(
                    bottom: state.getProfilePost[index] == state.getProfilePost.last ? 30.h : 0.h,
                    top: index == 0 ? 8.h : 0),
                child: _buildPostItem(post, index),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildOfflineMessage(bool showOfflineMessage) {
    return Positioned(
      bottom: 20.h,
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: _showOfflineMessage ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: _showOfflineMessage ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.95),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Theme.of(context).customColors.white,
                  size: 18.sp,
                ),
                buildSizedBoxW(8),
                Text(
                  "Couldn't refresh feed",
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool isVideo(String filePath) {
    if (filePath.isEmpty) return false;

    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    final lowerPath = filePath.toLowerCase();

    return videoExtensions.any((ext) => lowerPath.contains(ext));
  }
}
