// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:io';
import 'package:flowkar/core/helpers/vibration_helper.dart';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/model/edit_brand_by_id_model.dart';
import 'package:flowkar/features/authentication/model/forgot_password_response_model.dart';
import 'package:flowkar/features/authentication/model/get_brands_model.dart';
import 'package:flowkar/features/authentication/model/get_curent_brand_id_model.dart';
import 'package:flowkar/features/authentication/model/get_user_and_industry_type.dart';
import 'package:flowkar/features/authentication/model/get_user_status.dart';
import 'package:flowkar/features/authentication/model/login_response_model.dart';
import 'package:flowkar/features/authentication/model/permissions_model.dart';
import 'package:flowkar/features/authentication/model/register_response_model.dart';
import 'package:flowkar/features/authentication/model/reset_password_response_model.dart';
import 'package:flowkar/features/authentication/model/stored_user_account_model.dart';
import 'package:flowkar/features/authentication/model/switch_user_account_model.dart';
import 'package:flowkar/features/authentication/model/verify_otp_response_model.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/social_connect/bloc/social_connec_bloc.dart';
import 'package:flowkar/core/services/multi_account_manager.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  ApiClient apiClient = ApiClient(Dio());
  Timer? timer;
  int remainingSeconds = 60;
  AuthBloc(super.initialState) {
    on<AuthInitial>(_onInitialize);
    on<PasswordVisibilityEvent>(_changePasswordVisibility);
    on<LogInEvent>(_onSignInEvent);
    on<SignUpAPIEvent>(_onSignUpEvent);
    on<ChoosePasswordVisibilityEvent>(_choosePasswordVisibility);
    on<ConfirmPasswordVisibilityEventS>(_confirmPasswordVisibility);
    on<ForgotpasswordEvent>(_onForgotPasswordEvent);
    on<VerifyOtpEvent>(_onVerifyOtpEvent);
    on<ResendOtpEvent>(_onResendOtpEvent);
    on<StartOtpTimerEvent>(_onStartOtpTimerEvent);
    on<ResetPasswordEvent>(_onResetPasswordEvent);
    on<OneSignalIdApiEvent>(_sendOneSignalId);
    on<GEtUserAndIndustryTypeAPI>(_getUserAndIndustry);
    on<SelectUserAndIndustryType>(_onSelectUserAndIndustryType);
    on<GetUserStatusApi>(_getUserStatus);
    on<GetBrandsByBrIDAPI>(_getBrandById);
    on<DeleteBrandsByIdAPI>(_getDeleteBrandById);
    on<EditBrandAPI>(_geteditBrandById);
    on<CurentBrandIDAPI>(_getCurentBrandId);
    on<RegisterBrandsAPI>(_registerbrandApicall);
    on<LogOutAPI>(_logOutApicall);
    on<SwitchAccountEvent>(_onSwitchAccountEvent);
    // Multi-account events
    on<AddAccountLoginEvent>(_onAddAccountLoginEvent);
    // on<SwitchToStoredAccountEvent>(_onSwitchToStoredAccountEvent);
    on<LoadStoredAccountsEvent>(_onLoadStoredAccountsEvent);
    on<RemoveStoredAccountEvent>(_onRemoveStoredAccountEvent);
  }
  _onInitialize(
    AuthInitial event,
    Emitter<AuthState> emit,
  ) async {
    timer?.cancel();
    emit(state.copyWith(
      isShowPassword: false,
      isLoginLoading: false,
      isSignUpLoading: false,
      isChooseShowPassword: false,
      isConfirmShowPassword: false,
      isForgotPasswordLoading: false,
      remainingSeconds: 60,
      isUploadLoadong: false,
      isgetUserAndIndustryLoadong: false,
      iseditbrandLiading: false,
      idGetbrandbyIdLoading: false,
    ));
  }

  _changePasswordVisibility(
    PasswordVisibilityEvent event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(
      isShowPassword: event.value,
    ));
  }

  _choosePasswordVisibility(
    ChoosePasswordVisibilityEvent event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(
      isChooseShowPassword: event.value,
    ));
  }

  _confirmPasswordVisibility(
    ConfirmPasswordVisibilityEventS event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(
      isConfirmShowPassword: event.value,
    ));
  }

  Future<void> _onSignInEvent(LogInEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isLoginLoading: true));
    try {
      final result = await apiClient.login(event.email, event.password);
      if (result.status == true) {
        Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
        SocketService.emit(APIConfig.joinSocket, {'Authorization': result.token});
        SocketService.response(APIConfig.joinSocket, (joinSocket) {});
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message,
            style: GoogleFonts.montserrat(fontSize: 12.0.sp, fontWeight: FontWeight.w500),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        VibrationHelper.singleShortBuzz();
        await _storeAllPermissions(true);

        brandNameNotifier.value = result.username;

        await Prefobj.preferences?.put(Prefkeys.LOG_IN_USER_ID, result.userId.toString());
        Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
        Prefobj.preferences?.put(Prefkeys.PROFILE, result.profileImage);
        if (result.profileImage == "" || result.profileImage.isEmpty) {
          profileImageNotifier.value = AssetConstants.pngUser;
        } else {
          profileImageNotifier.value = result.profileImage;
        }
        // Batch storage operations to reduce I/O overhead
        await OptimizedStorage.batchPut({
          Prefkeys.AUTHTOKEN: result.token.toString(),
          Prefkeys.USER_ID: result.userId.toString(),
          Prefkeys.STATUS: result.userstatus,
          Prefkeys.NAME: result.name.toString(),
          Prefkeys.USERNAME: result.username.toString(),
          Prefkeys.LINKEDIN: result.linkedIn,
          Prefkeys.VIMEO: result.vimeo,
          Prefkeys.PINTEREST: result.pinterest,
          Prefkeys.REDDIT: result.reddit,
          Prefkeys.TUMBLR: result.tumblr,
          Prefkeys.INSTAGRAM: result.instagram,
          Prefkeys.FACEBOOK: result.facebook,
          Prefkeys.THREAD: result.threads,
          Prefkeys.YOUTUBE: result.youtube,
          Prefkeys.TIKTOK: result.tiktok,
          Prefkeys.TELEGRAM: result.telegram,
          Prefkeys.MASTODON: result.mastodon,
        });

        // Update notifiers after storage
        linkedInNotifier.value = result.linkedIn;
        vimeoNotifier.value = result.vimeo;
        pinterestNotifier.value = result.pinterest;
        redditNotifier.value = result.reddit;
        tumblrNotifier.value = result.tumblr;
        instagramNotifier.value = result.instagram;
        facebookNotifier.value = result.facebook;
        threadNotifier.value = result.threads;
        telegramNotifier.value = result.telegram;
        tiktokNotifier.value = result.tiktok;
        mastodonNotifier.value = result.mastodon;

        youtubeNotifier.value = result.youtube;
        tiktokNotifier.value = result.tiktok;
        await Prefobj.preferences?.put(Prefkeys.TIKTOK, tiktokNotifier.value);
        xNotifier.value = result.x;
        await Prefobj.preferences?.put(Prefkeys.X, xNotifier.value);
        telegramNotifier.value = result.telegram;
        await Prefobj.preferences?.put(Prefkeys.TELEGRAM, telegramNotifier.value);
        mastodonNotifier.value = result.mastodon;
        await Prefobj.preferences?.put(Prefkeys.MASTODON, mastodonNotifier.value);
        socialPlatformsStatus.value = {
          'VIMEO': result.vimeo,
          'FACEBOOK': result.facebook,
          'INSTAGRAM': result.instagram,
          'THREAD': result.threads,
          'LINKEDIN': result.linkedIn,
          'PINTEREST': result.pinterest,
          'TUMBLR': result.tumblr,
          'REDDIT': result.reddit,
          'YOUTUBE': result.youtube,
          'TIKTOK': result.tiktok,
          "X": result.x,
          "TELEGRAM": result.telegram,
          "MASTODON": result.mastodon,
        };
        // =================================================================================================

        // NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
        bool isPlatformConnected = result.isAnyAuth;
        Logger.lOG("CHECK SOCIAL PLATFORM CONNECTED IN SIGNIN:$isPlatformConnected");
        emit(state.copyWith(isLoginLoading: false));
        await _getUserStatus(GetUserStatusApi(), emit);
        // Store account for multi-account support after getting user status
        try {
          String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
          int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

          final userStatusResult = await apiClient.getUserStatus(logInUserId: loginUserId, brandid: brandId.toString());
          if (userStatusResult.status == true) {
            final storedAccount = MultiAccountManager.createFromLoginResponse(
              brandId: userStatusResult.brandId,
              name: result.name,
              profileImage: result.profileImage,
              username: result.username,
              userId: result.userId,
              token: result.token,
            );
            await MultiAccountManager.addAccount(storedAccount, setAsCurrent: true);
            Logger.lOG("Account stored for multi-account support");
          }
        } catch (error) {
          Logger.lOG("Error storing account for multi-account support: $error");
        }

        if (result.userstatus == "2") {
          await NavigatorService.pushAndRemoveUntil(AppRoutes.userTypeSelectionScreen);
          // } else if (result.userstatus == "3") {
          //   int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

          //   await _getBrandById(GetBrandsByBrIDAPI(brandId: brandId), emit);
          //   await NavigatorService.pushAndRemoveUntil(AppRoutes.brandregistrationscreen, arguments: [brandId, false]);
        } else if (result.userstatus == "3") {
          Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
          isPlatformConnected
              ? await NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar)
              : await NavigatorService.pushAndRemoveUntil(
                  AppRoutes.socialConnect,
                  arguments: [false],
                );
          await Prefobj.preferences?.put(Prefkeys.STATUS, result.userstatus);
        } else {
          await NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
          await Prefobj.preferences?.put(Prefkeys.STATUS, result.userstatus);
        }
        emit(state.copyWith(isLoginLoading: false));

        await Prefobj.preferences?.put(Prefkeys.AUTHTOKEN, result.token.toString());
        await Prefobj.preferences?.put(Prefkeys.USER_ID, result.userId.toString());
        // linkedInNotifier.value = result.linkedIn;
        // await Prefobj.preferences?.put(Prefkeys.LINKEDIN, linkedInNotifier.value);
        // vimeoNotifier.value = result.vimeo;
        // await Prefobj.preferences?.put(Prefkeys.VIMEO, vimeoNotifier.value);
        // pinterestNotifier.value = result.pinterest;
        // await Prefobj.preferences?.put(Prefkeys.PINTEREST, pinterestNotifier.value);
        // redditNotifier.value = result.reddit;
        // await Prefobj.preferences?.put(Prefkeys.REDDIT, redditNotifier.value);
        // tumblrNotifier.value = result.tumblr;
        // await Prefobj.preferences?.put(Prefkeys.TUMBLR, tumblrNotifier.value);
        // instagramNotifier.value = result.instagram;
        // await Prefobj.preferences?.put(Prefkeys.INSTAGRAM, instagramNotifier.value);
        // facebookNotifier.value = result.facebook;
        // await Prefobj.preferences?.put(Prefkeys.FACEBOOK, facebookNotifier.value);
        // threadNotifier.value = result.threads;
        // await Prefobj.preferences?.put(Prefkeys.THREAD, threadNotifier.value);
        // youtubeNotifier.value = result.youtube;
        // // youtubeNotifier.value = false;
        // await Prefobj.preferences?.put(Prefkeys.YOUTUBE, youtubeNotifier.value);
        // tiktokNotifier.value = result.tiktok;
        // await Prefobj.preferences?.put(Prefkeys.TIKTOK, tiktokNotifier.value);
        // socialPlatformsStatus.value = {
        //   'VIMEO': result.vimeo,
        //   'FACEBOOK': result.facebook,
        //   'INSTAGRAM': result.instagram,
        //   'THREAD': result.threads,
        //   'LINKEDIN': result.linkedIn,
        //   'PINTEREST': result.pinterest,
        //   'TUMBLR': result.tumblr,
        //   'REDDIT': result.reddit,
        //   'YOUTUBE': result.youtube,
        //   'TIKTOK': result.tiktok,
        // };
        Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
        emit(state.copyWith(
          loginResponseModel: result,
          isLoginLoading: false,
          isShowPassword: true,
        ));
      } else {
        emit(state.copyWith(isLoginLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isLoginLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

// Sign Up
  Future<void> _onSignUpEvent(SignUpAPIEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isSignUpLoading: true));
    try {
      Logger.lOG("refferenceCode: ${event.refferenceCode}");
      final result = event.refferenceCode.isNotEmpty
          ? await apiClient.register(
              email: event.email,
              name: event.name,
              password: event.confirmPassword,
              phone: '',
              username: event.userName,
              refferenceCode: event.refferenceCode,
            )
          : await apiClient.register(
              email: event.email,
              name: event.name,
              password: event.confirmPassword,
              phone: '',
              username: event.userName,
            );

      if (result.status == true) {
        SocketService.emit(APIConfig.joinSocket, {'Authorization': result.data.token});
        SocketService.response(
          APIConfig.joinSocket,
          (joinSocket) {},
        );

        if (result.data.profileImage == "") {
          profileImageNotifier.value = AssetConstants.pngUser;
        } else {
          Prefobj.preferences?.put(Prefkeys.PROFILE, result.data.profileImage);
          profileImageNotifier.value = result.data.profileImage;
        }
        await Prefobj.preferences?.put(Prefkeys.USER_ID, result.data.userId.toString());
        await Prefobj.preferences?.put(Prefkeys.AUTHTOKEN, result.data.token);
        await Prefobj.preferences?.put(Prefkeys.NAME, result.data.name.toString());
        await Prefobj.preferences?.put(Prefkeys.LOG_IN_USER_ID, result.data.userId.toString());

        linkedInNotifier.value = result.linkedIn;
        await Prefobj.preferences?.put(Prefkeys.LINKEDIN, linkedInNotifier.value);
        vimeoNotifier.value = result.vimeo;
        await Prefobj.preferences?.put(Prefkeys.VIMEO, vimeoNotifier.value);
        pinterestNotifier.value = result.pinterest;
        await Prefobj.preferences?.put(Prefkeys.PINTEREST, pinterestNotifier.value);
        redditNotifier.value = result.reddit;
        await Prefobj.preferences?.put(Prefkeys.REDDIT, redditNotifier.value);
        tumblrNotifier.value = result.tumblr;
        await Prefobj.preferences?.put(Prefkeys.TUMBLR, tumblrNotifier.value);
        instagramNotifier.value = result.instagram;
        await Prefobj.preferences?.put(Prefkeys.INSTAGRAM, instagramNotifier.value);
        facebookNotifier.value = result.facebook;
        await Prefobj.preferences?.put(Prefkeys.FACEBOOK, facebookNotifier.value);
        threadNotifier.value = result.threads;
        await Prefobj.preferences?.put(Prefkeys.THREAD, threadNotifier.value);
        youtubeNotifier.value = result.youtube;
        // youtubeNotifier.value = false;
        await Prefobj.preferences?.put(Prefkeys.YOUTUBE, youtubeNotifier.value);
        tiktokNotifier.value = result.tiktok;
        await Prefobj.preferences?.put(Prefkeys.TIKTOK, tiktokNotifier.value);
        xNotifier.value = result.x;
        await Prefobj.preferences?.put(Prefkeys.X, xNotifier.value);
        telegramNotifier.value = result.telegram;
        await Prefobj.preferences?.put(Prefkeys.TELEGRAM, telegramNotifier.value);
        mastodonNotifier.value = result.mastodon;
        await Prefobj.preferences?.put(Prefkeys.MASTODON, mastodonNotifier.value);
        socialPlatformsStatus.value = {
          'VIMEO': result.vimeo,
          'FACEBOOK': result.facebook,
          'INSTAGRAM': result.instagram,
          'THREAD': result.threads,
          'LINKEDIN': result.linkedIn,
          'PINTEREST': result.pinterest,
          'TUMBLR': result.tumblr,
          'REDDIT': result.reddit,
          'YOUTUBE': result.youtube,
          'TIKTOK': result.tiktok,
          'X': result.x,
          'TELEGRAM': result.telegram,
          'MASTODON': result.mastodon,
        };
        // =================================================================================

        // Store account for multi-account support after signup
        try {
          final storedAccount = StoredUserAccount(
            brandId: 0, // Will be updated after user type selection
            name: result.data.name,
            profileImage: result.data.profileImage,
            username: result.data.username,
            userId: result.data.userId,
            token: result.data.token,
          );
          await MultiAccountManager.addAccount(storedAccount, setAsCurrent: true);
          Logger.lOG("Signup account stored for multi-account support");
        } catch (error) {
          Logger.lOG("Error storing signup account for multi-account support: $error");
        }

        NavigatorService.pushAndRemoveUntil(AppRoutes.userTypeSelectionScreen);
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(registerResponseModel: result, isSignUpLoading: false));
      } else {
        emit(state.copyWith(isSignUpLoading: true));
      }
    } catch (error) {
      emit(state.copyWith(isSignUpLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  description: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

// Forgot Password
  Future<void> _onForgotPasswordEvent(ForgotpasswordEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isForgotPasswordLoading: true));

    try {
      final result = await apiClient.forgotPassword(email: event.email);
      if (result.status == true) {
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message.toString(),
            maxLines: 2,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        PersistentNavBarNavigator.pushNewScreen(event.context,
            screen: VerifyEmailPage(
              email: event.email.trim(),
              // stackonScreen: true,
            ));
        // NavigatorService.pushNamed(AppRoutes.verifyEmailPage, arguments: event.email.trim());
        emit(AuthState(forgotPasswordResponseModel: result, isForgotPasswordLoading: false));
      } else {
        emit(state.copyWith(isForgotPasswordLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isForgotPasswordLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                )
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  // Verify OTP
  Future<void> _onVerifyOtpEvent(VerifyOtpEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isVerifyEmailLoading: true));
    try {
      final result = await apiClient.verifyOtp(userOtp: event.userotp, email: event.email);

      if (result.status == true) {
        timer?.cancel();
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          description: Text(
            maxLines: 2,
            result.message.toString(),
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        Prefobj.preferences?.put(Prefkeys.TMPAUTHTOKEN, result.token!);
        NavigatorService.pushReplacement(AppRoutes.createNewPasswordPage);
        emit(state.copyWith(verifyOtpResponseModel: result, isVerifyEmailLoading: false));
      } else {
        emit(state.copyWith(isVerifyEmailLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isVerifyEmailLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onStartOtpTimerEvent(StartOtpTimerEvent event, Emitter<AuthState> emit) async {
    try {
      timer?.cancel();
      remainingSeconds = 60;

      emit(state.copyWith(remainingSeconds: remainingSeconds));
      final completer = Completer<void>();
      timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        remainingSeconds--;
        if (remainingSeconds < 0) {
          timer.cancel();
          completer.complete();
        } else {
          emit(state.copyWith(remainingSeconds: remainingSeconds));
        }
      });
      await completer.future;
    } catch (e) {
      timer?.cancel();
    }
  }

  Future<void> _onResendOtpEvent(ResendOtpEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isResedOtpLoading: true));
    try {
      final result = await apiClient.forgotPassword(email: event.email);
      if (result.status == true) {
        toastification.show(
          type: ToastificationType.info,
          showProgressBar: false,
          description: Text(
            maxLines: 2,
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(forgotPasswordResponseModel: result, isResedOtpLoading: false));
      } else {
        emit(state.copyWith(isResedOtpLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isResedOtpLoading: false));
      handleError(error);
    }
  }

  Future<void> _onResetPasswordEvent(ResetPasswordEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isResetPasswordLoading: true));
    try {
      timer?.cancel();
      final result = await apiClient.resetPassword(newPassword: event.newpassword);
      if (result.status == true) {
        NavigatorService.pushAndRemoveUntil(AppRoutes.resetPasswordSuccessScreen);
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message.toString(),
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        emit(state.copyWith(resetPasswordResponseModel: result, isResetPasswordLoading: false));
      } else {
        emit(state.copyWith(isResetPasswordLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isResetPasswordLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  @override
  Future<void> close() {
    timer?.cancel();
    return super.close();
  }

  _sendOneSignalId(OneSignalIdApiEvent event, Emitter<AuthState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.sendoneSignalIdApi(
          logInUserId: loginuserId, onesignalId: event.onesignalId, brandid: brandId.toString());
      Logger.lOG(result);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  Future<void> _getUserAndIndustry(GEtUserAndIndustryTypeAPI event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isgetUserAndIndustryLoadong: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getUserAndIndustryType(logInUserId: loginuserId, brandid: brandId.toString());

      if (result.status == true) {
        emit(state.copyWith(getUserAndIndustryType: result, isgetUserAndIndustryLoadong: false));
      } else {
        emit(state.copyWith(isgetUserAndIndustryLoadong: false));
      }
    } catch (error) {
      emit(state.copyWith(isgetUserAndIndustryLoadong: false));
      Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _onSelectUserAndIndustryType(SelectUserAndIndustryType event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isUploadLoadong: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.selectUserAndIndustryType(
        loginuserId,
        event.industry.isEmpty ? "" : event.industry,
        event.type,
        brandId.toString(),
      );

      if (result.status == true) {
        // int brandid = result.brandid;
        await Prefobj.preferences?.put(Prefkeys.BRANDID, result.brandid);
        await Prefobj.preferences?.put(Prefkeys.STATUS, result.userStatus);
        await Prefobj.preferences?.put(Prefkeys.USERTYPE, event.type);
        emit(state.copyWith(isUploadLoadong: false));
        if (event.isChangeUserType) {
          NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
        } else {
          await NavigatorService.pushAndRemoveUntil(AppRoutes.socialConnect, arguments: [false]);
        }
        // await _getBrandById(GetBrandsByBrIDAPI(brandId: brandid), emit);
        // await NavigatorService.pushNamed(AppRoutes.brandregistrationscreen, arguments: [result.brandid, false]);

        emit(state.copyWith(isUploadLoadong: false));
      } else {
        emit(state.copyWith(isUploadLoadong: false));
      }
    } catch (error) {
      emit(state.copyWith(isUploadLoadong: false));
      Logger.lOG("Dio--- Error: $error");
    }
  }

  Future<void> _getUserStatus(GetUserStatusApi event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isgetUserAndIndustryLoadong: true));

    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
      int? brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getUserStatus(logInUserId: loginUserId, brandid: brandId.toString());

      if (result.status == true) {
        bool isPlatformConnected = result.isAnyAuth;
        await Prefobj.preferences?.put(Prefkeys.STATUS, result.userStatus);
        await Prefobj.preferences?.put(Prefkeys.BRANDID, result.brandId);
        await Prefobj.preferences?.put(Prefkeys.USERNAME, result.username);
        int brandID = Prefobj.preferences?.get(Prefkeys.BRANDID);
        Logger.lOG("getUserStatus Brand Id $brandID");
        if (result.userStatus == "2") {
          await NavigatorService.pushAndRemoveUntil(AppRoutes.userTypeSelectionScreen);
          // } else if (result.userStatus == "3") {
          //   await _getBrandById(GetBrandsByBrIDAPI(brandId: result.brandId), emit);
          //   await Prefobj.preferences?.put(Prefkeys.BRANDID, result.brandId);
          //   await NavigatorService.pushAndRemoveUntil(AppRoutes.brandregistrationscreen, arguments: [result.brandId, false]);
        } else if (result.userStatus == "3") {
          isPlatformConnected
              ? await NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar)
              : await NavigatorService.pushAndRemoveUntil(AppRoutes.socialConnect, arguments: [false]);
          await Prefobj.preferences?.put(Prefkeys.STATUS, result.userStatus);
          await Prefobj.preferences?.put(Prefkeys.BRANDID, result.brandId);
        } else {
          await NavigatorService.pushAndRemoveUntil(AppRoutes.bottomNavBar);
        }
        emit(state.copyWith(getUserStatus: result, isgetUserAndIndustryLoadong: false));
      } else {
        emit(state.copyWith(isgetUserAndIndustryLoadong: false));
      }
    } catch (error) {
      emit(state.copyWith(isgetUserAndIndustryLoadong: false));
      Logger.lOG("getUserStatus Error: $error");
    }
  }

  Future<void> _getBrandById(GetBrandsByBrIDAPI event, Emitter<AuthState> emit) async {
    emit(state.copyWith(idGetbrandbyIdLoading: true));
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';
      final result = await apiClient.getBrandsbyId(loginUserId, event.brandId);

      if (result.status == true) {
        // SurveyDialog.show(event.context);
        await Prefobj.preferences?.put(Prefkeys.USERNAME, result.data.first.name);

        emit(state.copyWith(getBrandsModel: result, idGetbrandbyIdLoading: false));
      } else {
        emit(state.copyWith(idGetbrandbyIdLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(idGetbrandbyIdLoading: false));
      Logger.lOG("_getBrandById Error: $error");
    }
  }

  Future<void> _getDeleteBrandById(DeleteBrandsByIdAPI event, Emitter<AuthState> emit) async {
    emit(state.copyWith(idDeletebrandbyIdLoading: true));
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';
      final result = await apiClient.getDeleteBrandsbyId(loginUserId, event.brandId);

      if (result.status == true) {
        // event.context?.read<UserManagementBloc>().add(GetBrandsAPI());

        NavigatorService.goBack();

        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          description: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        emit(state.copyWith(idDeletebrandbyIdLoading: false));
      } else {
        NavigatorService.goBack();
        toastification.show(
          type: ToastificationType.warning,
          showProgressBar: false,
          description: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(idDeletebrandbyIdLoading: false));
      }
    } catch (error) {
      NavigatorService.goBack();
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.warning,
                  showProgressBar: false,
                  description: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                )
          : Logger.lOG("Dio--- Error: $error");
      emit(state.copyWith(idDeletebrandbyIdLoading: false));
      Logger.lOG("_getBrandById Error: $error");
    }
  }

  Future<void> _geteditBrandById(EditBrandAPI event, Emitter<AuthState> emit) async {
    emit(state.copyWith(iseditbrandLiading: true));
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';
      final result = await apiClient.editBrand(
          logInUserId: loginUserId,
          name: event.name,
          brandId: event.brandid,
          domain: event.domain,
          email: event.email,
          logoFile: event.filepath);

      if (result.status == true) {
        await Prefobj.preferences?.put(Prefkeys.BRANDID, event.brandid);
        await Prefobj.preferences!.put(Prefkeys.STATUS, result.userStatus);

        // event.context?.read<UserManagementBloc>().add(GetBrandsAPI());
        NavigatorService.goBack();

        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        emit(state.copyWith(iseditbrandLiading: false));
      } else {
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(iseditbrandLiading: false));
      }
    } catch (error) {
      emit(state.copyWith(iseditbrandLiading: false));
      Logger.lOG("_getBrandById Error: $error");
    }
  }

  Future<void> _registerbrandApicall(RegisterBrandsAPI event, Emitter<AuthState> emit) async {
    emit(state.copyWith(iseditbrandLiading: true));
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? '';
      String subscriptionId = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTION);
      final result = await apiClient.registerBrand(
          logInUserId: loginUserId,
          subscriptionId: subscriptionId.toString(),
          name: event.name,
          domain: event.domain,
          email: event.email,
          logoFile: event.filepath);

      if (result.status == true) {
        // event.context?.read<UserManagementBloc>().add(GetBrandsAPI());
        PersistentNavBarNavigator.pop(event.context!);

        // NavigatorService.goBack();

        emit(state.copyWith(iseditbrandLiading: false));
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      } else {
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(iseditbrandLiading: false));
      }
    } catch (error) {
      emit(state.copyWith(iseditbrandLiading: false));
      Logger.lOG("_registerbrandApi Error: $error");
    }
  }

  Future<void> _getCurentBrandId(CurentBrandIDAPI event, Emitter<AuthState> emit) async {
    emit(state.copyWith(swichbranchLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      final socialBloc = event.context?.read<SocialConnecBloc>();
      final result = await apiClient.getCurentBrandID(loginuserId, event.brandId.toString());

      if (result.status == true) {
        await Prefobj.preferences?.put(Prefkeys.BRANDID, result.brandId);
        int brandID = Prefobj.preferences?.get(Prefkeys.BRANDID);
        Logger.lOG("getCurentBrandID Brand Id $brandID");
        socialBloc?.add(SocialConnectCheckApiEvent());
        emit(state.copyWith(getCurentBrandIdModel: result, swichbranchLoading: false));
      } else {
        emit(state.copyWith(swichbranchLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(swichbranchLoading: false));
      Logger.lOG("_getBrandById Error: $error");
    }
  }

  Future<void> _logOutApicall(LogOutAPI event, Emitter<AuthState> emit) async {
    emit(state.copyWith(logOutLoading: true));
    try {
      final result = await apiClient.logOut();

      if (result.status == true) {
        // Comprehensive data cleanup
        await DataCleanupService.clearAllData();

        VibrationHelper.singleShortBuzz();
        NavigatorService.pushAndRemoveUntil(AppRoutes.signinPage);

        emit(state.copyWith(logOutLoading: false));
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
      } else {
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            result.message ?? '',
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(logOutLoading: false));
      }
      Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
    } catch (error) {
      emit(state.copyWith(logOutLoading: false));
      Logger.lOG("_logOutApicall Error: $error");
      Logger.lOG("Login user Id:--- ${Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? ''}");
    }
  }

  Future<void> _onSwitchAccountEvent(SwitchAccountEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isSwitchUserAccountLoading: true));
    try {
      // Step 1: Clear user-specific data before switching

      Logger.lOG("Clearing user data for brand account switch...");

      // Use DataCleanupService to clear user data properly
      await DataCleanupService.clearUserDataForAccountSwitch();

      // Step 2: Update brand ID in preferences before API call

      // Step 3: Perform account switch with selected brand
      String? playerIdString = Prefobj.preferences?.get(Prefkeys.SUBSCRIPTIONID);
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
      final result = await apiClient.getSwitchUser(
        brand: event.brandId.toString(),
        logInUserId: loginUserId.toString(),
        onesignalplayer: playerIdString.toString(),
      );
      Logger.lOG("Clearing user data for account switch to brand: ${event.brandName} (ID: ${event.brandId})");
      await DataCleanupService.clearUserDataForAccountSwitch();

      if (result.status == true) {
        // ==============================================================

        final success = await MultiAccountManager.switchToAccount(event.accountIndex);

        // ==============================================================
        // Step 3: Update socket connection with new user credentials
        SocketService.emit(APIConfig.joinSocket, {'Authorization': result.data.token});
        SocketService.response(APIConfig.joinSocket, (joinSocket) {});

        // Step 4: Show success message

        VibrationHelper.singleShortBuzz();

        // Step 5: Update global notifiers with new user data
        brandNameNotifier.value = result.data.username;

        await Prefobj.preferences?.put(Prefkeys.PROFILE, result.data.profileImage);
        if (result.data.profileImage == "" || result.data.profileImage.isEmpty) {
          profileImageNotifier.value = AssetConstants.pngUser;
        } else {
          profileImageNotifier.value = result.data.profileImage;
        }
        if (success) {
          final updatedAccountsList = await MultiAccountManager.getStoredAccounts();
          final currentAccount = updatedAccountsList?.currentAccount;

          if (currentAccount != null) {
            // Step 3: Update socket connection with new user credentials
            SocketService.emit(APIConfig.joinSocket, {'Authorization': currentAccount.token});
            SocketService.response(APIConfig.joinSocket, (joinSocket) {});

            final homeFeedBloc = event.context.read<HomeFeedBloc>();

            final userProfileBloc = event.context.read<UserProfileBloc>();
            // ignore: invalid_use_of_visible_for_testing_member
            homeFeedBloc.emit(homeFeedBloc.state.copyWith(
              posts: [],
              video: [],
              savedposts: [],
              isDiscoverposts: [],
              isuserProfileposts: [],
              getProfilePost: [],
              getProfilevideo: [],
              tagPostData: [],
              getUserTextPostData: [],
              newStoryData: [],
              reels: [],
            ));

            // ignore: invalid_use_of_visible_for_testing_member
            userProfileBloc.emit(userProfileBloc.state.copyWith(
              userProfile: null,
              highlightStoryData: [],
              followList: [],
              followingList: [],
            ));

            // Step 4: Clear additional user data
            await DataCleanupService.clearUserDataForAccountSwitch();
            await _handleUserPermissions(
                loginUserId, currentAccount.brandId.toString(), currentAccount.userId.toString());

            // Step 5: Refresh all data for the new account

            Logger.lOG("Refreshing data for switched account: ${currentAccount.name}");

            await Future.wait([
              // User profile data
              Future(() => event.context.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false))),

              // User posts and content
              Future(() => event.context.read<HomeFeedBloc>().add(FetchUserProfilePostEvent(page: 1))),
              Future(() => event.context.read<HomeFeedBloc>().add(FetchUserProfileVideoEvent(page: 1))),
              Future(() => event.context.read<HomeFeedBloc>().add(GetTagPostApi(tagpage: 1))),
              Future(() => event.context.read<HomeFeedBloc>().add(GetUserTextPostEvent(page: 1))),
              // Stories and highlights
              Future(() => event.context.read<UserProfileBloc>().add(GetHighlightStoryApiEvent())),

              // Home feed data (for main feed refresh)
              Future(() => event.context.read<HomeFeedBloc>().add(GetAllPostApiEvent(page: 1))),
              Future(() => event.context.read<HomeFeedBloc>().add(GetAllVideoApiEvent(page: 1))),
              Future(() => event.context.read<HomeFeedBloc>().add(GetNewStoryApiEvent())),

              // User management data (brands list)
              // Future(() => event.context.read<UserManagementBloc>().add(GetBrandsAPI())),
              Future(() => event.context.read<SocialConnecBloc>().add(SocialConnectCheckApiEvent())),
            ]);
          }

          // Step 6: Update global notifiers with new user data
          brandNameNotifier.value = currentAccount?.username ?? '';

          if (currentAccount?.profileImage == null || currentAccount?.profileImage == '') {
            profileImageNotifier.value = AssetConstants.pngUser;
          } else {
            profileImageNotifier.value = currentAccount?.profileImage ?? "";
          }

          emit(state.copyWith(
            isSwitchUserAccountLoading: false,
            storedAccountsList: updatedAccountsList,
            loginResponseModel: LoginResponseModel(
              brandId: currentAccount?.brandId ?? 0,
              status: true,
              message: 'Account switched successfully',
              name: currentAccount?.name ?? '',
              profileImage: currentAccount?.profileImage ?? '',
              username: currentAccount?.username ?? '',
              userId: currentAccount?.userId ?? 0,
              token: currentAccount?.token ?? '',
              userstatus: "1", // Assuming active user status
              isAnyAuth: false, // Default value for account switch
              facebook: false,
              instagram: false,
              youtube: false,
              linkedIn: false,
              pinterest: false,
              tiktok: false,
              threads: false,
              dailymotion: false,
              x: false,
              tumblr: false,
              vimeo: false,
              telegram: false,
              mastodon: false,
              reddit: false,
              isAdmin: false,
            ),
          ));

          toastification.show(
            type: ToastificationType.success,
            showProgressBar: false,
            title: Text(
              'Switched to ${currentAccount?.name ?? "account"}',
              style: GoogleFonts.montserrat(fontSize: 12.0.sp, fontWeight: FontWeight.w500),
            ),
            autoCloseDuration: const Duration(seconds: 3),
          );

          Logger.lOG("Multi-account switch completed successfully for user: ${currentAccount?.name}");
        } else {
          emit(state.copyWith(isSwitchUserAccountLoading: false));
          Logger.lOG("Failed to switch to stored account");
        }
        // Step 6: Batch storage operations to reduce I/O overhead
        await OptimizedStorage.batchPut({
          Prefkeys.AUTHTOKEN: result.data.token.toString(),
          Prefkeys.USER_ID: result.data.userId.toString(),
          Prefkeys.STATUS: result.data.userStatus,
          Prefkeys.NAME: result.data.name.toString(),
          Prefkeys.LINKEDIN: result.data.linkedIn,
          Prefkeys.VIMEO: result.data.vimeo,
          Prefkeys.PINTEREST: result.data.pinterest,
          Prefkeys.REDDIT: result.data.reddit,
          Prefkeys.TUMBLR: result.data.tumblr,
          Prefkeys.INSTAGRAM: result.data.instagram,
          Prefkeys.FACEBOOK: result.data.facebook,
          Prefkeys.THREAD: result.data.threads,
          Prefkeys.YOUTUBE: result.data.youTube,
          Prefkeys.TIKTOK: result.data.tiktok,
        });

        // Step 7: Update social platform notifiers with new user's connected platforms
        linkedInNotifier.value = result.data.linkedIn;
        vimeoNotifier.value = result.data.vimeo;
        pinterestNotifier.value = result.data.pinterest;
        redditNotifier.value = result.data.reddit;
        tumblrNotifier.value = result.data.tumblr;
        instagramNotifier.value = result.data.instagram;
        facebookNotifier.value = result.data.facebook;
        threadNotifier.value = result.data.threads;
        youtubeNotifier.value = result.data.youTube;
        tiktokNotifier.value = result.data.tiktok;
        telegramNotifier.value = result.data.telegram;
        mastodonNotifier.value = result.data.mastodon;
        xNotifier.value = result.data.x;

        await Prefobj.preferences?.put(Prefkeys.TIKTOK, tiktokNotifier.value);
        xNotifier.value = result.data.x;
        await Prefobj.preferences?.put(Prefkeys.X, xNotifier.value);
        telegramNotifier.value = result.data.telegram;
        await Prefobj.preferences?.put(Prefkeys.TELEGRAM, telegramNotifier.value);
        mastodonNotifier.value = result.data.mastodon;
        await Prefobj.preferences?.put(Prefkeys.MASTODON, mastodonNotifier.value);
        socialPlatformsStatus.value = {
          'VIMEO': result.data.vimeo,
          'FACEBOOK': result.data.facebook,
          'INSTAGRAM': result.data.instagram,
          'THREAD': result.data.threads,
          'LINKEDIN': result.data.linkedIn,
          'PINTEREST': result.data.pinterest,
          'TUMBLR': result.data.tumblr,
          'REDDIT': result.data.reddit,
          'YOUTUBE': result.data.youTube,
          'TIKTOK': result.data.tiktok,
          'X': result.data.x,
          'TELEGRAM': result.data.telegram,
          'MASTODON': result.data.mastodon,
        };

        // Step 8: Update user status and complete the switch
        bool isPlatformConnected = result.data.isAnyAuth;
        Logger.lOG("CHECK SOCIAL PLATFORM CONNECTED IN ACCOUNT SWITCH: $isPlatformConnected");

        // Step 9: Emit final state with new user data
        emit(state.copyWith(
          switchUserAccountModel: result,
          isSwitchUserAccountLoading: false,
        ));

        Logger.lOG("Account switch completed successfully for user: ${result.data.name}");
      } else {
        emit(state.copyWith(isSwitchUserAccountLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isSwitchUserAccountLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.warning,
                  showProgressBar: false,
                  description: Text(
                    error.response?.data['message'] == "User not in brand"
                        ? "You are not invited or Did not accept the invite request for the user."
                        : error.response?.data['message'] ?? 'Something went wrong',
                    // error.response?.data['message'] ?? error.message ?? '',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  // Multi-account event handlers
  Future<void> _onAddAccountLoginEvent(AddAccountLoginEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isLoginLoading: true));
    try {
      final result = await apiClient.addswithaccountlogin(event.email, event.password);
      if (result.status == true) {
        // Get brand ID from user status
        // final userStatusResult = await apiClient.getUserStatus();
        int brandId = result.brandId;

        // Create stored account
        final storedAccount = MultiAccountManager.createFromLoginResponse(
          brandId: brandId,
          name: result.name,
          profileImage: result.profileImage,
          username: result.username,
          userId: result.userId,
          token: result.token,
        );

        // Add account to storage without setting as current (preserve original user)
        await MultiAccountManager.addAccount(storedAccount, setAsCurrent: false);

        // Load updated accounts list
        final updatedAccountsList = await MultiAccountManager.getStoredAccounts();

        emit(state.copyWith(
          isLoginLoading: false,
          storedAccountsList: updatedAccountsList,
          // Don't update loginResponseModel to preserve current user's data
        ));

        // Navigate back to profile screen
        if (event.context != null) {
          Navigator.of(event.context!).pop();
        }

        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            'Account added successfully',
            style: GoogleFonts.montserrat(fontSize: 12.0.sp, fontWeight: FontWeight.w500),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        emit(state.copyWith(isLoginLoading: false));
      } else {
        emit(state.copyWith(isLoginLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isLoginLoading: false));
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.warning,
                  showProgressBar: false,
                  description: Text(
                    maxLines: 2,
                    error.response?.data['message'] == "Please Sign Up Before Sign In"
                        ? "Account does not exist"
                        : error.response?.data['message'] ?? 'Something went wrong',
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
      handleError(error);
      Logger.lOG("Add account error: $error");
    }
  }

  Future<void> _onLoadStoredAccountsEvent(LoadStoredAccountsEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isLoadingStoredAccounts: true));
    try {
      final accountsList = await MultiAccountManager.getStoredAccounts();
      emit(state.copyWith(
        isLoadingStoredAccounts: false,
        storedAccountsList: accountsList,
      ));
    } catch (error) {
      emit(state.copyWith(isLoadingStoredAccounts: false));
      Logger.lOG("Load stored accounts error: $error");
    }
  }

  Future<void> _onRemoveStoredAccountEvent(RemoveStoredAccountEvent event, Emitter<AuthState> emit) async {
    try {
      await MultiAccountManager.removeAccountByUserId(event.accountIndex);
      final updatedAccountsList = await MultiAccountManager.getStoredAccounts();
      emit(state.copyWith(storedAccountsList: updatedAccountsList));

      toastification.show(
        type: ToastificationType.success,
        showProgressBar: false,
        title: Text(
          'Account removed successfully',
          style: GoogleFonts.montserrat(fontSize: 12.0.sp, fontWeight: FontWeight.w500),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      NavigatorService.goBack();
    } catch (error) {
      Logger.lOG("Remove account error: $error");
    }
  }

  Future<void> _handleUserPermissions(String loginUserId, String brandId, String currentUserId) async {
    try {
      Logger.lOG("=== PERMISSION HANDLING DEBUG ===");
      Logger.lOG("Login User ID: $loginUserId");
      Logger.lOG("Current User ID: $currentUserId");
      Logger.lOG("Brand ID: $brandId");
      Logger.lOG("Are they same user? ${loginUserId == currentUserId}");
      // Check if login user is switching back to their own account
      if (loginUserId == currentUserId) {
        Logger.lOG("Login user switching back to own account - granting all permissions");
        // Grant all permissions when login user switches back to their own account
        await _storeAllPermissions(true);
      } else {
        Logger.lOG("🔄 User switching to different account - fetching permissions from API");
        // Fetch permissions from API for other users
        final currentUserRole = await apiClient.getUserPermissions(
          logInUserId: loginUserId,
          brandid: brandId,
        );

        Logger.lOG("API Response Status: ${currentUserRole.status}");
        Logger.lOG("API Response Data: ${currentUserRole.data}");
        Logger.lOG("API Response Message: ${currentUserRole.message}");
        if (currentUserRole.status == true && currentUserRole.data != null) {
          await _storePermissions(currentUserRole.data);
          Logger.lOG("Permissions stored successfully for user: $currentUserId");
        } else {
          // If API fails, set all permissions to false
          await _storeAllPermissions(false);
          Logger.lOG("❌ API failed - setting all permissions to false");
        }
      }
      await _printCurrentPermissions();
    } catch (error) {
      Logger.lOG("Error fetching user permissions: $error");
      // On error, set all permissions to false for security
      await _storeAllPermissions(false);
    }
  }

  /// Store permissions based on API response
  Future<void> _storePermissions(List<int> permissionIds) async {
    // Define permission mapping
    final Map<int, String> permissionMap = {
      1: Prefkeys.PRM_POST,
      2: Prefkeys.PRM_MESSAGE,
      3: Prefkeys.PRM_ANALYTICS,
      4: Prefkeys.PRM_USER_MANGEMENT,
      5: Prefkeys.PRM_BRAND_MANGEMENT,
      6: Prefkeys.PRM_BLOCK_UNBLOCK,
      7: Prefkeys.PRM_FEEDBACK,
    };

    // Prepare batch data for optimized storage
    Map<String, dynamic> permissionData = {};

    // Set all permissions to false first
    for (String permissionKey in permissionMap.values) {
      permissionData[permissionKey] = false;
    }

    // Set permissions to true for IDs present in response
    for (int permissionId in permissionIds) {
      if (permissionMap.containsKey(permissionId)) {
        permissionData[permissionMap[permissionId]!] = true;
      }
    }
    // Special condition: If 8 is present (regardless of 1), or if both 1 and 8 are present, set PRM_POST to true
    if (permissionIds.contains(8)) {
      permissionData[Prefkeys.PRM_POST] = true;
    }
    // Use batch storage for better performance
    await OptimizedStorage.batchPut(permissionData);

    Logger.lOG("Permissions stored: $permissionData");

    isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    isUserManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_USER_MANGEMENT) ?? false;
    isBrandManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BRAND_MANGEMENT) ?? false;
    isBlockUnblockPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    isFeedbackPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_FEEDBACK) ?? false;
  }

  Future<void> _storeAllPermissions(bool value) async {
    final Map<String, dynamic> permissionData = {
      Prefkeys.PRM_POST: value,
      Prefkeys.PRM_MESSAGE: value,
      Prefkeys.PRM_ANALYTICS: value,
      Prefkeys.PRM_USER_MANGEMENT: value,
      Prefkeys.PRM_BRAND_MANGEMENT: value,
      Prefkeys.PRM_BLOCK_UNBLOCK: value,
      Prefkeys.PRM_FEEDBACK: value,
    };

    await OptimizedStorage.batchPut(permissionData);
    Logger.lOG("All permissions set to: $value");
    isPostPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isMassagePermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    isanalyticsPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    isUserManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_USER_MANGEMENT) ?? false;
    isBrandManagementPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BRAND_MANGEMENT) ?? false;
    isBlockUnblockPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    isFeedbackPermissionNotifier.value = Prefobj.preferences?.get(Prefkeys.PRM_FEEDBACK) ?? false;
  }

  Future<void> _printCurrentPermissions() async {
    Logger.lOG("=== CURRENT PERMISSIONS IN STORAGE ===");
    Logger.lOG("PRM_POST: ${Prefobj.preferences?.get(Prefkeys.PRM_POST)}");
    Logger.lOG("PRM_MESSAGE: ${Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE)}");
    Logger.lOG("PRM_ANALYTICS: ${Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS)}");
    Logger.lOG("PRM_USER_MANGEMENT: ${Prefobj.preferences?.get(Prefkeys.PRM_USER_MANGEMENT)}");
    Logger.lOG("PRM_BRAND_MANGEMENT: ${Prefobj.preferences?.get(Prefkeys.PRM_BRAND_MANGEMENT)}");
    Logger.lOG("PRM_BLOCK_UNBLOCK: ${Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK)}");
    Logger.lOG("PRM_FEEDBACK: ${Prefobj.preferences?.get(Prefkeys.PRM_FEEDBACK)}");
    Logger.lOG("=== END PERMISSIONS ===");
  }
}
