// ignore_for_file: unused_element, deprecated_member_use

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/reward_leader/presentation/pages/reward_leader.dart';
import 'package:flowkar/features/authentication/bloc/auth_bloc.dart';
import 'package:flowkar/features/feedback_screen/presentation/page/feedback_screen.dart';
import 'package:flowkar/features/join_beta_tester/presentation/beta_user_signup_popup.dart';
import 'package:flowkar/features/profile_screen/presentation/page/scheduled_post_screen.dart';
import 'package:flowkar/features/profile_screen/presentation/page/share_profile_screen.dart';
import 'package:flowkar/features/setting_screen/bloc/setting_bloc.dart';
import 'package:flowkar/features/setting_screen/page/user_type_selection_screen.dart';
import 'package:flowkar/features/setting_screen/page/drat_post/draft_post_screen.dart';
import 'package:flowkar/features/setting_screen/page/panding_aprovel_post/panding_aprovel_post.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/presentation/planner_calendar.dart';
import 'package:flowkar/features/setting_screen/page/save_post/presentation/save_post_page.dart';
import 'package:flowkar/features/subscription/presentation/page/subscription_plan_screen.dart';
import 'package:flowkar/features/survey_form/bloc/survey_bloc.dart';
import 'package:flowkar/features/upload_post/bloc/post_bloc.dart';
import 'package:flowkar/features/user_management/page/add_user_roll_screen.dart';
import 'package:flowkar/features/user_management/page/user_management_screen.dart';
import 'package:flowkar/features/wallet/wallet.dart';
import 'package:flowkar/features/widgets/bloc_user/blocked_users_list_id.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flowkar/core/helpers/vibration_helper.dart';

class SettingScreen extends StatefulWidget {
  final String refrelCode;
  const SettingScreen({super.key, this.refrelCode = ""});
  // static Widget builder(BuildContext context) {
  //   return SettingScreen(
  //     refrelCode: ,
  //   );
  // }

  @override
  State<SettingScreen> createState() => _SettingScreenState();
}

class _SettingScreenState extends State<SettingScreen> {
  int? brandId;
  String? userType;
  late bool isPostPermission;
  late bool isMessagePermission;
  late bool isAnalyticsPermission;
  late bool isUserManagePermission;
  late bool isBrandManagePermission;
  late bool isBlockPermission;
  late bool isFeedbackPermission;
  late bool isLoginUser;

  @override
  initState() {
    super.initState();
    Logger.lOG("refrelCode: ${widget.refrelCode}");
    brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
    userType = Prefobj.preferences?.get(Prefkeys.USERTYPE);
    // brandId = int.parse(brandid);
  }

  void logConnectedPlatforms() {
    final connectedPlatforms =
        socialPlatformsStatus.value.entries.where((entry) => entry.value).map((entry) => entry.key).toList();

    Logger.lOG("Connected Platforms: $connectedPlatforms");
  }

  @override
  Widget build(BuildContext context) {
    isPostPermission = Prefobj.preferences?.get(Prefkeys.PRM_POST) ?? false;
    isMessagePermission = Prefobj.preferences?.get(Prefkeys.PRM_MESSAGE) ?? false;
    isAnalyticsPermission = Prefobj.preferences?.get(Prefkeys.PRM_ANALYTICS) ?? false;
    isUserManagePermission = Prefobj.preferences?.get(Prefkeys.PRM_USER_MANGEMENT) ?? false;
    isBrandManagePermission = Prefobj.preferences?.get(Prefkeys.PRM_BRAND_MANGEMENT) ?? false;
    isBlockPermission = Prefobj.preferences?.get(Prefkeys.PRM_BLOCK_UNBLOCK) ?? false;
    isFeedbackPermission = Prefobj.preferences?.get(Prefkeys.PRM_FEEDBACK) ?? false;
    isLoginUser = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) == Prefobj.preferences?.get(Prefkeys.USER_ID);
    return Padding(
      padding: EdgeInsets.only(right: 3.w, left: 3.w),
      child: Scaffold(
        appBar: _buildAppBar(context),
        body: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, authState) {
            return BlocBuilder<PostBloc, PostState>(
              builder: (context, state) {
                int platformconnect = socialPlatformsStatus.value.values.where((connected) => connected).length;
                Logger.lOG(socialPlatformsStatus.value);
                logConnectedPlatforms();
                return BlocBuilder<SurveyBloc, SurveyState>(
                  builder: (context, surveyState) {
                    return SingleChildScrollView(
                      child: Column(
                        children: [
                          buildSizedBoxH(8.h),
                          _buildsocialAccounts(context, platformconnect),
                          if (widget.refrelCode.isNotEmpty) buildSizedBoxH(20.h),
                          if (widget.refrelCode.isNotEmpty) _buildrefrelCode(context, widget.refrelCode),
                          buildSizedBoxH(20.h),
                          // Visibility(
                          //   visible: surveyState.userHomeDataModel?.isbetatester == true ? false : true,
                          //   child: Dismissible(
                          //     key: ValueKey("join_beta_post"),
                          //     background: Container(
                          //       color: Theme.of(context).primaryColor.withOpacity(0.4),
                          //       child: Icon(Icons.delete, color: Colors.white),
                          //     ),
                          //     direction: DismissDirection.horizontal,
                          //     onDismissed: (_) async {
                          //       await Prefobj.preferences
                          //           ?.put(Prefkeys.BETA_POST_DISMISSED_AT, DateTime.now().millisecondsSinceEpoch);
                          //     },
                          //     child: Column(
                          //       children: [
                          //         _buildJoinBeta(context, platformconnect),
                          //         buildSizedBoxH(20.h),
                          //       ],
                          //     ),
                          //   ),
                          // ),
                          // _buildSubcriptionFeatures(context, state),
                          // buildSizedBoxH(20.h),
                          _buildWalletButton(context),
                          if (isPostPermission || isBlockPermission) buildSizedBoxH(20.h),
                          _buildSettingFeatures(context, state),
                          buildSizedBoxH(20.h),
                          _buildUsermanagement(context),
                          buildSizedBoxH(20.h),
                          _buildPrivacyPolicyAndFeedback(context),
                          buildSizedBoxH(20.h),
                          if (isLoginUser) _buildDeleteAccountButton(context),
                          // buildSizedBoxH(20.h),
                          // _builddarkModecontainer(context),
                          buildSizedBoxH(20.h),
                          if (isLoginUser) _buildLogoutButton(context),
                          if (isLoginUser) buildSizedBoxH(20.h),
                        ],
                      ),
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  void showLogoutPopup(BuildContext context, AuthState state) {
    showDialog(
      barrierDismissible: state.logOutLoading,
      context: context,
      builder: (ctx) {
        return StatefulBuilder(builder: (context, setState) {
          return BlocBuilder<AuthBloc, AuthState>(
            builder: (context, authState) {
              return CustomAlertDialog(
                imagePath: Assets.images.svg.svgLogOut.path,
                title: Lang.of(context).lbl_log_out,
                subtitle: Lang.of(context).msg_logout,
                onConfirmButtonPressed: () {
                  // Prefobj.preferences?.clear();
                  // Prefobj.preferences?.delete(Prefkeys.AUTHTOKEN);
                  // Prefobj.preferences?.delete(Prefkeys.TMPAUTHTOKEN);
                  // Prefobj.preferences?.delete(Prefkeys.LOG_IN_USER_ID);
                  // socialPlatformsStatus.value = {
                  //   'VIMEO': false,
                  //   'FACEBOOK': false,
                  //   'INSTAGRAM': false,
                  //   'THREAD': false,
                  //   'LINKEDIN': false,
                  //   'PINTEREST': false,
                  //   'TUMBLR': false,
                  //   'REDDIT': false,
                  //   'YOUTUBE': false,
                  //   'TIKTOK': false,
                  // };
                  context.read<AuthBloc>().add(LogOutAPI());
                },
                confirmButtonText: Lang.of(context).lbl_yes,
                cancelButtonText: Lang.of(context).lbl_no,
                isLoading: authState.logOutLoading,
              );
            },
          );
        });
      },
    );
  }

  void showDeleteAccountPopup(BuildContext context, PostState state) {
    showDialog(
      barrierDismissible: state.isdeleteAccountLoading,
      context: context,
      builder: (ctx) {
        return BlocBuilder<PostBloc, PostState>(
          builder: (context, state) {
            return StatefulBuilder(builder: (context, setState) {
              return CustomAlertDialog(
                imagePath: Assets.images.svg.setting.svgDailogDeleteAccount.path,
                title: "Confirm Account Deletion",
                subtitle: Lang.of(context).msg_delete_account_subtitle,
                onConfirmButtonPressed: () async {
                  context.read<PostBloc>().add(DeleteAccountsEvent(context: context));
                },
                confirmButtonText: Lang.of(context).lbl_delete,
                cancelButtonText: Lang.of(context).lbl_Cancel,
                isLoading: state.isdeleteAccountLoading,
              );
            });
          },
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Settings",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
        ),
      ],
    );
  }

  Widget _buildsocialAccounts(BuildContext context, int platformconnect) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GestureDetector(
        onTap: isLoginUser
            ? () {
                PersistentNavBarNavigator.pushNewScreen(
                  context,
                  screen: SocialConnectPage(
                    stackonScreen: true,
                    oncallBack: () {
                      setState(() {});
                    },
                  ),
                );
              }
            : () {
                showToastNoPermission(access: 'social media accountts');
              },
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [
              BoxShadow(
                offset: Offset(0, 2),
                blurRadius: 16.r,
                spreadRadius: 0,
                color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(left: 20.w, top: 10.h, right: 10.w, bottom: 10.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Social Media Accounts',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16.sp,
                                ),
                          ),
                          buildSizedBoxH(8.h),
                          Text(
                            'Control your social media connections.',
                            maxLines: 2,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 10.sp,
                                ),
                          ),
                        ],
                      ),
                    ),
                    buildSizedBoxW(16.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          height: 30.0.h,
                          width: 30.0.w,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Theme.of(context).primaryColor,
                            ),
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                          child: Center(
                            child: Text(
                              '$platformconnect',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14.sp,
                                  color: Theme.of(context).customColors.subtitlecolor),
                            ),
                          ),
                        ),
                        buildSizedBoxH(8.h),
                        Text(
                          'Connected',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w500,
                                fontSize: 12.sp,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
                buildSizedBoxH(12.h),
                Wrap(
                  runSpacing: 10.h,
                  spacing: 10.w,
                  // crossAxisAlignment: CrossAxisAlignment.center,
                  // mainAxisAlignment: MainAxisAlignment.start,
                  children: _buildConnectedPlatformIcons(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildrefrelCode(BuildContext context, String refrelCode) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GestureDetector(
        onTap: () {
          Clipboard.setData(ClipboardData(text: refrelCode));
          toastification.show(
            type: ToastificationType.success,
            showProgressBar: false,
            title: Text(
              "Copied to clipboard",
              style: GoogleFonts.montserrat(
                fontSize: 12.0.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            autoCloseDuration: const Duration(seconds: 3),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [
              BoxShadow(
                offset: Offset(0, 2),
                blurRadius: 16.r,
                spreadRadius: 0,
                color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(left: 20.w, top: 10.h, right: 10.w, bottom: 10.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Referral Code',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16.sp,
                                ),
                          ),
                          buildSizedBoxH(8.h),
                          Text(
                            refrelCode,
                            maxLines: 2,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14.sp,
                                ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.copy)
                  ],
                ),
                // buildSizedBoxH(12.h),
                // Wrap(
                //   runSpacing: 10.h,
                //   spacing: 10.w,
                //   // crossAxisAlignment: CrossAxisAlignment.center,
                //   // mainAxisAlignment: MainAxisAlignment.start,
                //   children: _buildConnectedPlatformIcons(),
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildJoinBeta(BuildContext context, int platformconnect) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GestureDetector(
        onTap: () {
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: SocialConnectPage(
              stackonScreen: true,
              oncallBack: () {
                setState(() {});
              },
            ),
          );
        },
        child: Container(
          height: 100.h,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [
              BoxShadow(
                offset: Offset(0, 2),
                blurRadius: 16.r,
                spreadRadius: 0,
                color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
              ),
            ],
            image: DecorationImage(
              image: AssetImage(Assets.images.pngs.setting.pngJoinBetaBg.path),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              buildSizedBoxH(30),
              Text(
                "Join Beta Tester App",
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
              ),
              buildSizedBoxH(10),
              CustomElevatedButton(
                height: 26.h,
                width: 86.w,
                text: "Join Beta",
                brderRadius: 6.r,
                onPressed: () {
                  VibrationHelper.singleShortBuzz();
                  showDialog(
                    context: context,
                    barrierDismissible: true,
                    builder: (BuildContext context) {
                      return const BetaUserSignupPopup();
                    },
                  );
                },
                buttonTextStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontSize: 10.sp,
                      color: Theme.of(context).customColors.white,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildConnectedPlatformIcons() {
    final connectedPlatforms =
        socialPlatformsStatus.value.entries.where((entry) => entry.value).map((entry) => entry.key).toList();

    final Map<String, String> platformImages = {
      'FACEBOOK': Assets.images.icons.social.facebook.path,
      'INSTAGRAM': Assets.images.icons.social.insta.path,
      'YOUTUBE': Assets.images.icons.social.icYoutube.path,
      'TIKTOK': Assets.images.icons.social.icTictok.path,
      'TWITTER': Assets.images.icons.social.twitter.path,
      'LINKEDIN': Assets.images.icons.social.linkedin.path,
      'PINTEREST': Assets.images.icons.social.pintrest.path,
      'THREAD': Assets.images.icons.social.icThread.path,
      'VIMEO': Assets.images.pngs.socialConnect.pngVimeo.path,
      'TUMBLR': Assets.images.pngs.socialConnect.icTumblr.path,
      'REDDIT': Assets.images.pngs.socialConnect.pngReddit.path,
      'X': Assets.images.pngs.socialConnect.pngTwitter.path,
      "TELEGRAM": Assets.images.icons.social.svgTelegram.path,
      'MASTODON': Assets.images.icons.social.svgMastodon.path,
    };

    List<Widget> widgets = [];
    for (String platform in connectedPlatforms) {
      widgets.add(
        _platformContainer(imagePath: platformImages[platform] ?? ''),
      );
      // widgets.add(buildSizedBoxW(12.w));
    }

    return widgets;
  }

  Widget _platformContainer({String? imagePath}) {
    return CustomImageView(
      imagePath: imagePath,
      height: 30.0.h,
      width: 30.0.h,
    );
  }

  Widget _buildSettingsIcons({String? imagePath, EdgeInsetsGeometry? padding}) {
    return Container(
      height: 40.0.h,
      width: 40.0.w,
      padding: padding ?? EdgeInsets.all(9.0),
      decoration: BoxDecoration(
          shape: BoxShape.circle, color: Theme.of(context).customColors.settingCardIconBGColor.withOpacity(0.2)),
      child: CustomImageView(
        imagePath: imagePath,
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildSeetingsWidget(
      {required String imagePath, required String text, Function()? onTap, EdgeInsetsGeometry? padding}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 14.0.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                _buildSettingsIcons(imagePath: imagePath, padding: padding),
                buildSizedBoxW(20.w),
                Text(
                  text,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 16.sp,
                        color: text == Lang.of(context).lbl_log_out
                            ? Theme.of(context).customColors.logoutTextColor
                            : Theme.of(context).customColors.black,
                      ),
                ),
              ],
            ),
            CustomImageView(
                margin: EdgeInsets.symmetric(horizontal: 14.0.w),
                imagePath: Assets.images.svg.setting.svgRightArrow.path),
          ],
        ),
      ),
    );
  }

  Widget _buildSubcriptionFeatures(BuildContext context, PostState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 2),
              blurRadius: 16.r,
              spreadRadius: 0,
              color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 11.0.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSeetingsWidget(
                imagePath: Assets.images.svg.setting.svgSubscription.path,
                text: "Subscription Plan",
                padding: EdgeInsets.all(9.0),
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context, screen: SubscriptionPlanScreen());
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingFeatures(BuildContext context, PostState state) {
    return isPostPermission || isBlockPermission
        ? Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.white,
                borderRadius: BorderRadius.circular(10.r),
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 2),
                    blurRadius: 16.r,
                    spreadRadius: 0,
                    color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 11.0.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (isPostPermission) ...[
                      _buildSeetingsWidget(
                        imagePath: Assets.images.svg.setting.svgSavedPosts.path,
                        text: Lang.of(context).lbl_saved_posts,
                        padding: EdgeInsets.all(6.0),
                        onTap: () {
                          // NavigatorService.pushNamed(AppRoutes.savepostpage);
                          PersistentNavBarNavigator.pushNewScreen(context, screen: SavePostPage());
                        },
                      ),
                      Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                      _buildSeetingsWidget(
                        imagePath: Assets.images.svg.setting.svgDraft.path,
                        text: Lang.of(context).lbl_draft_posts,
                        padding: EdgeInsets.only(
                          left: 6.0,
                          top: 10.0,
                          bottom: 8.0,
                        ),
                        onTap: () {
                          context.read<HomeFeedBloc>().add(GetDraftPostAPIEvent(draftPostPage: 1));

                          PersistentNavBarNavigator.pushNewScreen(context, screen: DraftPostScreen());
                        },
                      ),
                      Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                      if (isLoginUser) ...[
                        _buildSeetingsWidget(
                          imagePath: Assets.images.svg.setting.svgPendingPost.path,
                          text: Lang.of(context).lbl_panding_approvel_posts,
                          padding: EdgeInsets.only(
                            left: 3.0,
                            top: 7.0,
                            bottom: 7.0,
                          ),
                          onTap: () {
                            context.read<HomeFeedBloc>().add(GetDraftPostAPIEvent(draftPostPage: 1));

                            PersistentNavBarNavigator.pushNewScreen(context, screen: PandingAprovelPostScreen());
                          },
                        ),
                        Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                      ],
                      _buildSeetingsWidget(
                        imagePath: Assets.images.svg.setting.svgScheduleCalendar.path,
                        text: Lang.of(context).lbl_scheduled_post,
                        padding: EdgeInsets.all(10),
                        onTap: () {
                          PersistentNavBarNavigator.pushNewScreen(context, screen: ScheduledPostScreen());
                        },
                      ),
                      Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                      _buildSeetingsWidget(
                        imagePath: Assets.images.svg.setting.svgScheduledPosts.path,
                        text: "Planner",
                        padding: EdgeInsets.all(10),
                        onTap: () {
                          PersistentNavBarNavigator.pushNewScreen(context, screen: PlannerCalendar());
                        },
                      ),
                      Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                    ],
                    if (isBlockPermission)
                      _buildSeetingsWidget(
                        imagePath: Assets.images.svg.setting.svgBlockedPeople.path,
                        text: Lang.of(context).lbl_blocked_accounts,
                        padding: EdgeInsets.only(
                          left: 4.0,
                          top: 8.0,
                          bottom: 8.0,
                        ),
                        onTap: () {
                          PersistentNavBarNavigator.pushNewScreen(context, screen: BlockedUserListScreen());
                        },
                      ),
                  ],
                ),
              ),
            ),
          )
        : SizedBox.shrink();
  }

  Widget _builddarkModecontainer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.borderColor.withOpacity(0.08),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      CustomImageView(
                        imagePath: Assets.images.icons.other.icDarkMode.path,
                      ),
                      buildSizedBoxW(30.w),
                      Text(
                        Lang.of(context).lbl_dark,
                        style: Theme.of(context)
                            .textTheme
                            .titleLarge
                            ?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      BlocBuilder<SettingBloc, SettingState>(
                        builder: (context, state) {
                          return Transform.scale(
                            alignment: Alignment.centerRight,
                            scale: 0.7,
                            child: Switch(
                              value: state.isDarkMode,
                              onChanged: (value) {
                                context.read<SettingBloc>().add(ToggleDarkModeEvent());
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
              buildSizedBoxH(12.h),
              Padding(
                padding: EdgeInsets.only(right: MediaQuery.of(context).size.width * 0.23),
                child: Text(
                  Lang.of(context).lbl_change_dark_mode_msg,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500, fontSize: 12.sp),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

// Log Out
  Widget _buildLogoutButton(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0.w),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 11.0.w),
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.white,
              borderRadius: BorderRadius.circular(10.r),
              boxShadow: [
                BoxShadow(
                  offset: Offset(0, 2),
                  blurRadius: 16.r,
                  spreadRadius: 0,
                  color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
                ),
              ],
            ),
            child: _buildSeetingsWidget(
              imagePath: Assets.images.icons.other.icLogout.path,
              text: Lang.of(context).lbl_log_out,
              onTap: () {
                showLogoutPopup(context, state);
              },
            ),
          ),
        );
      },
    );
  }

// Delete Account
  Widget _buildDeleteAccountButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 11.0.w),
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 2),
              blurRadius: 16.r,
              spreadRadius: 0,
              color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
            ),
          ],
        ),
        child: BlocBuilder<PostBloc, PostState>(
          builder: (context, state) {
            return _buildSeetingsWidget(
              imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
              text: Lang.of(context).lbl_delete_account,
              onTap: () {
                showDeleteAccountPopup(context, state);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildWalletButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 11.0.w),
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 2),
              blurRadius: 16.r,
              spreadRadius: 0,
              color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
            ),
          ],
        ),
        child: Column(
          children: [
            if (isLoginUser) ...[
              _buildSeetingsWidget(
                imagePath: Assets.images.svg.setting.svgWallet.path,
                text: Lang.of(context).lbl_wallet,
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context, screen: WalletScreen());
                },
              ),
              Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1)
            ],
            _buildSeetingsWidget(
              imagePath: Assets.images.svg.setting.svgRewardLeader.path,
              text: Lang.of(context).lbl_reward_leader,
              padding: EdgeInsets.all(8),
              onTap: () {
                PersistentNavBarNavigator.pushNewScreen(context, screen: RewardLeaderScreen());
              },
            ),
          ],
        ),
      ),
    );
  }

// User management and Feedback
  Widget _buildUsermanagement(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 11.0.w),
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 2),
              blurRadius: 16.r,
              spreadRadius: 0,
              color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildSeetingsWidget(
              imagePath: Assets.images.svg.setting.svgShareProfile.path,
              text: Lang.of(context).lbl_share_profile,
              padding: EdgeInsets.all(10),
              onTap: () {
                PersistentNavBarNavigator.pushNewScreen(context, screen: ShareProfileScreen());
              },
            ),
            if (isUserManagementPermissionNotifier.value == true) ...[
              if (userType != "User")
                Column(
                  children: [
                    // Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                    // _buildSeetingsWidget(
                    //     imagePath: Assets.images.pngs.setting.pngMyBrands.path,
                    //     text: Lang.of(context).lbl_my_brands,
                    //     padding: EdgeInsets.all(4.0),
                    //     onTap: () {
                    //       PersistentNavBarNavigator.pushNewScreen(context, screen: BrandListScreen());
                    //     }),
                    Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                    _buildSeetingsWidget(
                      imagePath: Assets.images.svg.setting.svgRoles.path,
                      text: Lang.of(context).lbl_roles,
                      padding: EdgeInsets.only(left: 4.0, top: 8.0, bottom: 8.0),
                      onTap: () {
                        PersistentNavBarNavigator.pushNewScreen(context, screen: AddUserListScreen());
                      },
                    ),
                    Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                    _buildSeetingsWidget(
                        imagePath: Assets.images.svg.other.svgUserProfile.path,
                        text: Lang.of(context).lbl_users,
                        onTap: () {
                          PersistentNavBarNavigator.pushNewScreen(context, screen: UserManagementScreen());
                        }),
                  ],
                ),
              if (userType == "User")
                Column(
                  children: [
                    Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
                    _buildSeetingsWidget(
                        imagePath: Assets.images.svg.other.svgUserProfile.path,
                        text: "User Type",
                        onTap: () {
                          PersistentNavBarNavigator.pushNewScreen(context, screen: UserTypeSelectionSeetingScreen());
                        }),
                  ],
                ),
            ]
          ],
        ),
      ),
    );
  }

// Privacy Policy and Feedback
  Widget _buildPrivacyPolicyAndFeedback(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 11.0.w),
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 2),
              blurRadius: 16.r,
              spreadRadius: 0,
              color: Theme.of(context).customColors.settingCardShadowColor.withOpacity(0.10),
            ),
          ],
        ),
        child: Column(
          children: [
            if (isFeedbackPermission)
              _buildSeetingsWidget(
                imagePath: Assets.images.svg.setting.svgFeedback.path,
                text: Lang.of(context).lbl_feedback,
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(context, screen: FeedbackScreen(), withNavBar: false);
                },
              ),
            Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
            _buildSeetingsWidget(
              imagePath: Assets.images.svg.setting.svgPrivacyPolicy.path,
              text: Lang.of(context).lbl_privacy_policy,
              onTap: () {
                final Uri url = Uri.parse(APIConfig.policyurl);
                launchUrl(url, mode: LaunchMode.platformDefault);
              },
            ),
            Divider(color: Theme.of(context).customColors.dividerColor.withOpacity(0.1), height: 1),
            _buildSeetingsWidget(
              imagePath: Assets.images.svg.setting.svgTermsConditions.path,
              text: Lang.of(context).lbl_terms_conditions,
              onTap: () {
                final Uri url = Uri.parse(APIConfig.termsOfUse);
                launchUrl(url, mode: LaunchMode.platformDefault);
                // Logger.lOG("term URL --  ${url}");
              },
            ),
          ],
        ),
      ),
    );
  }
}
