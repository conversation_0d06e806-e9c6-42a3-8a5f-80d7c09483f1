import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/caption_preview_widget.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_player.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/presentation/planner_calendar.dart';
import 'package:intl/intl.dart';

class PostDetailsScreen extends StatefulWidget {
  final ScheduledPostAppointment appointment;

  const PostDetailsScreen({
    super.key,
    required this.appointment,
  });

  @override
  State<PostDetailsScreen> createState() => _PostDetailsScreenState();
}

class _PostDetailsScreenState extends State<PostDetailsScreen> {
  late FlickMultiManager flickMultiManager;
  final PageController _postcontroller = PageController();
  int currentMediaIndex = 0;
  @override
  void initState() {
    super.initState();
    flickMultiManager = FlickMultiManager();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildSizedBoxH(8.h),
            _buildUserDetail(context),
            _buildPostMedia(),
            _buildPostDetail(context),
            if (widget.appointment.scheduledPostData.platform.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildSizedBoxH(20.h),
                  Row(
                    children: [
                      buildSizedBoxW(16.w),
                      Expanded(
                        child: Text(
                          "Scheduled Platforms",
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
                        ),
                      ),
                    ],
                  ),
                  buildSizedBoxH(10.h),
                  _buildScheduledPlatforms(widget.appointment.scheduledPostData.platform),
                ],
              ),
            buildSizedBoxH(20.h),
            Row(
              children: [
                buildSizedBoxW(16.w),
                Expanded(
                  child: Text(
                    "Scheduled Information",
                    style:
                        Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
                  ),
                ),
              ],
            ),
            buildSizedBoxH(16.h),
            _buildDetailRow(
              context,
              'Scheduled Date',
              DateFormat('dd MMM, yyyy').format(widget.appointment.startTime),
              image: Assets.images.svg.setting.svgScheduleCalendar.path,
            ),
            buildSizedBoxH(13.h),
            _buildDetailRow(
              context,
              'Scheduled Time',
              DateFormat('hh:mm a').format(widget.appointment.startTime),
              icon: Icons.access_time_outlined,
            ),
            buildSizedBoxH(13.h),
            _buildDetailRow(
                context,
                'Created Date',
                DateFormat('dd MMM, yyyy HH:mm a')
                    .format(DateTime.parse(widget.appointment.scheduledPostData.createdAt)),
                // image: Assets.images.svg.profile.svgEdit.path,
                icon: Icons.edit_outlined),
            buildSizedBoxH(14.h),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          "Planner Post",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 18.sp,
              ),
        ),
      ],
    );
  }

  Widget _buildUserDetail(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.white,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).customColors.black.withOpacity(0.2),
            blurRadius: 2,
          )
        ],
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                ClipRRect(
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Container(
                      height: 43.0.h,
                      width: 43.0.w,
                      padding: (widget.appointment.scheduledPostData.user.profileImage == AssetConstants.pngUser ||
                                  widget.appointment.scheduledPostData.user.profileImage.isEmpty ||
                                  widget.appointment.scheduledPostData.user.profileImage == '') &&
                              widget.appointment.scheduledPostData.user.userId.toString() ==
                                  Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                          ? EdgeInsets.all(8)
                          : EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Theme.of(context).primaryColor.withOpacity(0.2),
                          width: 2,
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: CustomImageView(
                        onTap: () {},
                        radius: widget.appointment.scheduledPostData.user.userId.toString() ==
                                Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                            ? widget.appointment.scheduledPostData.user.profileImage.isEmpty
                                ? null
                                : BorderRadius.circular(100.0)
                            : BorderRadius.circular(100.0),
                        fit: (widget.appointment.scheduledPostData.user.profileImage == AssetConstants.pngUser ||
                                widget.appointment.scheduledPostData.user.profileImage.isEmpty ||
                                widget.appointment.scheduledPostData.user.profileImage == '')
                            ? BoxFit.contain
                            : BoxFit.cover,
                        imagePath: widget.appointment.scheduledPostData.user.profileImage.isEmpty
                            ? widget.appointment.scheduledPostData.user.userId.toString() ==
                                    Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                                ? AssetConstants.pngUser
                                : AssetConstants.pngUserReomve
                            : widget.appointment.scheduledPostData.user.profileImage,
                      ),
                    ),
                  ),
                ),
                buildSizedBoxW(8),
                Expanded(
                  child: InkWell(
                    onTap: () {},
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.appointment.scheduledPostData.user.name,
                          style: Theme.of(context).textTheme.titleLarge!.copyWith(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w700,
                                color: Color(0xff292D32),
                              ),
                        ),
                        buildSizedBoxH(1),
                        Text(
                          "@${widget.appointment.scheduledPostData.user.username}",
                          style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w700,
                                color: Color(0xff575353),
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostMedia() {
    return ConstrainedBox(
      constraints: BoxConstraints(maxHeight: 440.h, maxWidth: MediaQuery.of(context).size.width),
      child:
          // AspectRatio(
          //   aspectRatio: aspectRatio,
          //   child:
          SizedBox(
        //  width: widget.width! / MediaQuery.of(context).devicePixelRatio,
        // height: MediaQuery.of(context).size.width / aspectRatio,
        // height: calculatedHeight,
        // width: calculatedWidth,
        child: PageView.builder(
          controller: _postcontroller,
          onPageChanged: (index) {
            setState(() {
              currentMediaIndex = index;
              // _resetHideTimer();
            });
          },
          itemCount: widget.appointment.scheduledPostData.files.length,
          itemBuilder: (context, index) {
            final mediaUrl = widget.appointment.scheduledPostData.files[index];

            return isVideo(mediaUrl)
                ? FlickMultiPlayer(
                    key: ObjectKey(mediaUrl),
                    url: mediaUrl,
                    flickMultiManager: flickMultiManager,
                    image: (widget.appointment.scheduledPostData.thumbnailFiles.length > index &&
                            widget.appointment.scheduledPostData.thumbnailFiles[index].isNotEmpty)
                        ? widget.appointment.scheduledPostData.thumbnailFiles[index]
                        : AssetConstants.pngPlaceholder,
                  )
                : AspectRatio(
                    aspectRatio: 1,
                    child: CustomImageView(
                      imagePath: mediaUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                    ),
                  );
          },
        ),
      ),
    );
  }

  Widget _buildPostDetail(BuildContext context) {
    final post = widget.appointment.scheduledPostData;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
          color: Theme.of(context).customColors.white,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).customColors.black.withOpacity(0.2),
              blurRadius: 2,
            )
          ],
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(20.r),
            bottomRight: Radius.circular(20.r),
          )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // buildSizedBoxH(5.h),
                CaptionPreviewWidget(
                    caption:
                        "${post.title == "''" || post.title.isEmpty ? '' : post.title}${post.description.isEmpty ? '' : post.title == "''" || post.title.isEmpty ? post.description : "\n${post.description}"}",
                    comment: "",
                    commentonTap: () {},
                    isTextPost: false),
                buildSizedBoxH(4),
                // Text(
                //   getPostTimeAgoFromUTC(widget.postTime.toString()),
                //   style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                //         fontSize: 10.sp,
                //         fontWeight: FontWeight.w400,
                //       ),
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value, {String? image, IconData? icon}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          (image == null || image.isEmpty)
              ? Icon(
                  icon,
                  size: 22.sp,
                  color: Theme.of(context).primaryColor,
                )
              : CustomImageView(
                  imagePath: image,
                  height: 19.h,
                  width: 19.w,
                ),
          buildSizedBoxW(12.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontSize: 13.sp,
                    ),
              ),
              buildSizedBoxH(2.0),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScheduledPlatforms(Map<String, dynamic> platforms) {
    final activePlatforms = platforms.entries.where((entry) => entry.value == true).map((entry) => entry.key).toList();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          ...activePlatforms.map((platform) => Padding(
                padding: EdgeInsets.only(right: 8.w),
                child: CustomImageView(
                  imagePath: getplatformimgurL(platform),
                  height: 32.h,
                  width: 32.w,
                ),
              )),
        ],
      ),
    );
  }

  String? getplatformimgurL(String platform) {
    switch (platform.toLowerCase()) {
      case 'vimeo':
        return Assets.images.icons.social.v.path;
      case 'instagram':
        return Assets.images.icons.social.insta.path;
      case 'linkedin':
        return Assets.images.icons.social.linkedin.path;
      case 'pinterest':
        return Assets.images.icons.social.icPintrest.path;
      case 'reddit':
        return Assets.images.icons.social.icReddit.path;
      case 'tumblr':
        return Assets.images.icons.social.icTumblr.path;
      case 'threads':
        return Assets.images.icons.social.icThread.path;
      case 'facebook':
        return Assets.images.icons.social.facebook.path;
      case 'tiktok':
        return Assets.images.icons.social.icTictok.path;
      case 'youtube':
        return Assets.images.icons.social.icYoutube.path;
      case 'x':
        return Assets.images.icons.social.twitter.path;
      case 'blusky':
        return Assets.images.icons.social.svgBluesky.path;
      case 'telegram':
        return Assets.images.icons.social.svgTelegram.path;
      case 'mastodon':
        return Assets.images.icons.social.svgMastodon.path;
      default:
        return Assets.images.icons.other.icFlowkar.path;
    }
  }
}
