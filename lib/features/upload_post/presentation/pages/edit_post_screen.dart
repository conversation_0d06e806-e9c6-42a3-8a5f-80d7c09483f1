import 'package:detectable_text_field/widgets/detectable_text_editing_controller.dart';
import 'package:flowkar/core/utils/exports.dart';

class EditPostScreenState extends StatefulWidget {
  final dynamic post;
  final dynamic video;
  final int? index;
  final bool isvideo;
  final bool isTextPost;
  const EditPostScreenState(
      {super.key, required this.post, this.video, this.index, required this.isvideo, required this.isTextPost});
  static Widget builder(BuildContext context) {
    final List args = ModalRoute.of(context)?.settings.arguments as List;
    return EditPostScreenState(
      post: args[0], // Ensure non-null assignment
      video: args[1], // Ensure non-null assignment
      index: args.length > 2 ? args[2] : null, // Handle potential missing arguments
      isvideo: args.length > 3 ? args[3] as bool : false,
      isTextPost: args.length > 4 ? args[4] as bool : false,
    );
  }

  @override
  State<EditPostScreenState> createState() => _EditPostScreenStateState();
}

class _EditPostScreenStateState extends State<EditPostScreenState> {
  dynamic post;
  final DetectableTextEditingController postContentController = DetectableTextEditingController();
  final TextEditingController postTitleController = TextEditingController();
  bool textFieldIsEmpty = true;
  bool initial = true;
  bool isLoading = false;
  String textPostTitle = "";
  String textPostDescription = "";
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    post = widget.isvideo == true ? widget.video : widget.post;
    super.initState();
    Logger.lOG("widget.videowidget.videowidget.videowidget.video ${widget.isvideo}");
    Logger.lOG(' post?.user.userId, post?.user.userId,${widget.video?.user.userId}');
    Logger.lOG('Video==============,${widget.video?.id}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildPostAnalyticsAppBar(context),
      body: Form(
        key: _formKey,
        child: BlocBuilder<HomeFeedBloc, HomeFeedState>(
          builder: (context, state) {
            // if (state.anyliticsLoading) {
            //   return Center(child: LoadingAnimationWidget());
            // }
            if (initial == true) {
              postTitleController.text = post.title ?? '';
              postContentController.text = post.description ?? '';
              postTitleController.text = postTitleController.text.replaceAll("''", "");
              textPostTitle = post.title ?? '';
              textPostDescription = post.description ?? '';

              initial = false;
            }
            return SingleChildScrollView(
              child: InkWell(
                focusColor: Colors.transparent,
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildSizedBoxH(8),
                    // if(!widget.isTextPost)
                    PostWidget(
                      width: widget.isTextPost ? 0 : post.width ?? 0,
                      height: widget.isTextPost ? 0 : post.height ?? 0,
                      userByIDpost: false,
                      userByIDvideo: false,
                      userVideo: false,
                      userpost: false,
                      isTextPost: widget.isTextPost,
                      taggedIn: [],
                      state: state,
                      index: widget.index ?? 0,
                      userId: post?.user.userId,
                      latestcomments: '',
                      postId: post?.id ?? 0,
                      profileImage: post?.user.profileImage ?? '',
                      name: "${post?.user.name}",
                      username: "${post?.user.username}",
                      postMedia: widget.isTextPost ? [] : post.files ?? {},
                      title: "${post?.title == "''" || post!.title.isEmpty ? '' : post?.title}",
                      caption: widget.isTextPost
                          ? "$textPostTitle\n$textPostDescription"
                          : "${post?.title == "''" || post!.title.isEmpty ? '' : post?.title}${post!.description.isEmpty ? '' : post?.title == "''" || post!.title.isEmpty ? post?.description : "\n${post?.description}"}",
                      likes: "${post?.likes}",
                      comments: "${post?.commentsCount}",
                      thumbnailImage: [],
                      postTime: '',
                      isLiked: false,
                      isSaved: false,
                      editePost: true,
                      doubleTap: () {},
                      likeonTap: () {},
                      commentonTap: () {},
                      shareonTap: () {},
                      saveonTap: () {},
                    ),
                    buildSizedBoxH(16),
                    // _buildTitle_Description("Title", context),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: FlowkarTextFormField(
                        borderDecoration: OutlineInputBorder(
                            // borderSide: const BorderSide(color: AppColors.textfieldborder),
                            borderRadius: BorderRadius.all(
                          Radius.circular(10.0.r),
                        )),
                        context: context,
                        onChanged: (value) {
                          textPostTitle = value;
                          setState(() {});
                        },
                        labelText: Lang.of(context).lbl_title,
                        controller: postTitleController,
                      ),
                    ),
                    buildSizedBoxH(12.0),
                    // _buildTitle_Description(Lang.of(context).lbl_description, context),
                    Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: TextFormField(
                          controller: postContentController,
                          textInputAction: TextInputAction.newline,
                          keyboardType: TextInputType.multiline,
                          maxLines: 6,
                          minLines: 2,
                          maxLength: 1000,
                          style: TextStyle(color: Colors.black),
                          decoration: InputDecoration(
                            alignLabelWithHint: true,
                            labelStyle: TextStyle(color: Colors.black),
                            labelText: 'Description',
                            floatingLabelBehavior: FloatingLabelBehavior.auto,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            focusedErrorBorder: OutlineInputBorder(
                              borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: (value) {
                            if (widget.isTextPost && (value == null || value.trim().isEmpty)) {
                              return 'Description is required for text posts';
                            }
                            return null;
                          },
                          onChanged: (value) {
                            textPostDescription = value;
                            setState(() {});
                          },
                        )),

                    Center(child: _buildDoneButton(state))
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildPostAnalyticsAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(widget.isTextPost ? "Edit Text Post " : "Edit Post",
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp)),
      ],
    );
  }

  Widget _buildDoneButton(HomeFeedState homeFeedState) {
    return CustomElevatedButton(
      alignment: Alignment.center,
      onPressed: () {
        if (_formKey.currentState?.validate() ?? false) {
          setState(() {
            isLoading = true;
          });
          widget.isTextPost
              ? context.read<HomeFeedBloc>().add(EditTextPostAPIEvent(
                    postId: post?.id,
                    description: postContentController.text,
                    title: postTitleController.text,
                  ))
              : context.read<HomeFeedBloc>().add(UpdatePostApiEvent(
                    postId: "${post?.id}",
                    description: postContentController.text.trim(),
                    title: postTitleController.text.trim(),
                    isvideo: widget.isvideo,
                  ));
          Future.delayed(const Duration(seconds: 2), () {
            NavigatorService.goBack();
            isLoading = false;
          });
        }
      },
      margin: EdgeInsets.only(top: 30.0.h, bottom: 20.0.h),
      text: "Done",
      isDisabled: isLoading,
      isLoading: isLoading,
    );
  }
  // Widget _buildDoneButton(HomeFeedState homeFeedState) {
  //   return CustomElevatedButton(
  //     alignment: Alignment.center,
  //     onPressed: () {
  //       setState(() {
  //         isLoading = true;
  //       });
  //       context.read<HomeFeedBloc>().add(UpdatePostApiEvent(
  //           postId: "${post?.id}",
  //           description: postContentController.text,
  //           title: postTitleController.text,
  //           isvideo: widget.isvideo));
  //       Future.delayed(const Duration(seconds: 2), () {
  //         NavigatorService.goBack();
  //         isLoading = false;
  //       });
  //     },
  //     margin: EdgeInsets.only(top: 30.0.h, bottom: 20.0.h),
  //     text: "Done",
  //     isDisabled: isLoading,
  //     isLoading: isLoading,
  //   );
  // }
}
