// ignore_for_file: non_constant_identifier_names, use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/deep_link_post/model/deep_link_post_model.dart';
import 'package:flowkar/features/discover/model/hashtag_post_model.dart';
import 'package:flowkar/features/discover/model/search_user_model.dart';
import 'package:flowkar/features/home_feed_screen/model/analytics_model.dart';
import 'package:flowkar/features/home_feed_screen/model/blocked_users_model.dart';
import 'package:flowkar/features/home_feed_screen/model/draft_post_model.dart';
import 'package:flowkar/features/home_feed_screen/model/get_comment_model.dart';
import 'package:flowkar/features/home_feed_screen/model/post_response_model.dart';
import 'package:flowkar/features/home_feed_screen/model/post_share_model.dart';
import 'package:flowkar/features/home_feed_screen/model/user_block_model.dart';
import 'package:flowkar/features/home_feed_screen/model/video_response_model.dart';
import 'package:flowkar/features/live_stream/model/live_user_model.dart';
import 'package:flowkar/features/notification/model/get_post_by_id_model.dart';
import 'package:flowkar/features/profile_screen/bloc/profile_bloc.dart';
import 'package:flowkar/features/profile_screen/model/get_follower_list_model.dart';
import 'package:flowkar/features/profile_screen/model/get_user_text_post_model.dart';
import 'package:flowkar/features/profile_screen/model/tag_post_list_model.dart';
import 'package:flowkar/features/profile_screen/model/user_profile_detail_model.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/model/planner_list_post_model.dart';
import 'package:flowkar/features/setting_screen/page/planner_calendar/model/scheduled_posts_web_model.dart';
import 'package:flowkar/features/setting_screen/page/save_post/model/save_post_model.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:flowkar/features/sm_chat/model/ai_generate_massage_or_comment_model.dart';
import 'package:flowkar/features/upload_post/model/user_profile_model.dart';
// import 'package:flowkar/features/story_module/New_story_code/new_story_model.dart';
import 'package:flowkar/features/story_module/model/upload_story_model.dart';
import 'package:http/http.dart' as http;

import '../../story_feature_demo/Story_view_demo/story_view/models/story_model.dart';

part 'home_feed_event.dart';
part 'home_feed_state.dart';

class HomeFeedBloc extends Bloc<HomeFeedEvent, HomeFeedState> with PerformanceMonitorMixin {
  ApiClient apiClient = ApiClient(Dio());

  HomeFeedBloc(super.initialState) {
    on<HomeFeedInitial>(_onInitialize);
    on<GetAllPostApiEvent>(_onFetchPosts);
    on<GetAllPostProgressiveEvent>(_onFetchPostsProgressive);
    on<GetAllVideoApiEvent>(_onFetchVideo);
    on<GetAllVideoProgressiveEvent>(_onFetchVideoProgressive);
    on<LikePostSocketEvent>(_onLikePostSocket);
    on<GetCommentApiEvent>(_onCommentGetApi);
    on<CommentPostSocketEvent>(_oncommentPostSocket);
    on<ReplycommentSocketEvent>(_onreplycommentSocket);
    on<UpdateReplyCommenEvent>(_onupdatereplyCommentEvent);
    on<ReplayCommentLikeSocketEvent>(_onreplayCommentlikeSocket);
    on<LikeCommentSocketEvent>(_onlikeCommentsocket);
    on<DeleteCommentApiEvent>(_onDeleteCommentApi);
    on<DeletePostApiEvent>(_onDeletePostApi);
    on<SavedPostSocketEvent>(_savedPostSocketEvent);
    on<SavedPostApiEvent>(_savedPostApiEvent);
    on<DiscoverPostApiEvent>(_ondiscoverPostApiEvent);

    on<AnalyticsApiEvent>(_onAnalyticsEvent);
    on<GetUserTextPostEvent>(_getuserTextPostApi);
    on<GetUserByIdTextPostApiIdEvent>(_getUserByIdTextPostApi);
    on<EditTextPostAPIEvent>(_onTextPostUpdate);

    on<GetUserProfilebyIdApi>(_getuserProfileApi);
    on<GetUserPostApiIdEvent>(_getuserPostApi);
    // on<FetchUserProfileEvent>(_getProfileApi);
    on<FetchUserProfilePostEvent>(_getProfilePostApi);
    on<FetchUserProfileVideoEvent>(_onprofileVideo);
    on<GetUserVideoByIdApiEvent>(_onprofileVideoByID);
    on<UpdatePostApiEvent>(_updatepostapicall);
    on<GetProfilePostEvent>(_onGetProfilePostEvent);

    // Story
    // on<GetAllStoryApiEvent>(_getallstory);
    on<StoryLikeSocketEvent>(_onStoryLikeSocket);
    on<GetNewStoryApiEvent>(_onNewStory);
    on<UploadStoryApiEvent>(_uploadStory);
    on<DeleteStoryEvent>(_deleteStory);
    on<GetTagPostApi>(_getTagPostApi);
    on<FollowUserLikeListSocketEvent>(_followuserLikeListSocketEvent);
    on<FollowUserSocketEvent>(_followUserSocket);
    // Block User
    on<BlockUserApiEvent>(_blockUserApiCall);
    on<GetblockedusersListApiEvent>(_getBlockedUsersListApi);

    //Hastag
    on<DiscoverHashtagPostListEvent>(_ondiscoverHashtagPostApiEvent);
    // Draft Post
    on<GetDraftPostAPIEvent>(_onGetDraftPostEvent);
    on<DeleteDraftPostAPIEvent>(_onDeleteDraftPostEvent);
    on<UploadDraftPostAPIEvent>(_onUploadDraftPostEvent);
    // Share Post
    on<PostShareEvent>(_postShareApiCall);
    on<SharePostMessageEvent>(_sharePostMessageApiCall);
    on<UserSearchQueryChanged>(_onSearchQueryChanged);
    on<ClearUserSearchHistory>(_onClearSearchHistory);
    on<RemoveUserSearchItem>(_onRemoveSearchItem);
    on<SharePostSearchUserListEvent>(_onUserSearchEvent);

    // Notification Post
    on<GetNotificationPostEvent>(_ongetNotificationPostApiEvent);

    // Deep Link Post
    on<FetchDeepLinkPostEvent>(_fetchDeepLinkPost);
    on<GetLivestoryApiEvent>(_ongetLiveUser);
    // Planner scheduled post web
    on<GetScheduledPostWebEvent>(_getScheduledPostWebApi);
    on<GetPlannerPostListEvent>(_getPlannerPostListWebApi);
    on<AIgenerateCommentEvent>(_aIgenerateCommentApi);
    on<ClearAIGeneratedCommentEvent>(_clearAigenerateComment);
  }

  FutureOr<void> _onInitialize(HomeFeedInitial event, Emitter<HomeFeedState> emit) {
    emit(state.copyWith(
      posts: [],
      video: [],
      isDiscoverposts: [],
      allVideoFetch: false,
      homeFeedVideoLoading: false,
      isVideoLoadingMore: false,
      allFetch: false,
      isLoadingMore: false,
      homeFeedLoading: false,
      isCommentloding: false,
      savePostallFetch: false,
      savedposts: [],
      savePosthasMore: true,
      isSavePostLoadingMore: false,
      isSavePostloding: false,
      isDiscoverLoadingMore: false,
      profileByIdLoading: false,
      profilPosteByIdLoadingmore: false,
      profilPosteByIdLoading: false,
      profilPosteByIdallFetch: false,
      isuserProfileposts: [],
      getProfilePost: [],
      getprofilPostedallFetch: false,
      getprofilrPosteLoading: false,
      getprofilPosteLoadingmore: false,
      getProfilevideo: [],
      getProfileallVideoFetch: false,
      getProfileisVideoLoadingMore: false,
      getProfileVideoLoading: false,
      getProfileByIDvideo: [],
      getProfileallByIDVideoFetch: false,
      getProfileisVideoByIDLoadingMore: false,
      getProfileVideoByIDLoading: false,
      searchController: TextEditingController(),
      tagPostData: [],
      isGetDraftPostLoading: false,
      draftPosts: [],
      getuserTextPostLoading: false,
      getUserTextPostData: [],
      getUserTextPostLoadingMore: false,
      getUserTextPostAllFetch: false,
      getUserByIdTextPostData: [],
      getUserByIdTextPostLoadingMore: false,
      getUserByIdTextPostAllFetch: false,
      getUserByIdTextPostPage: 1,
      getUserByIdTextPostLoading: false,
      isSchedulePostWebLoading: false,
      schedulePostWeb: [],
    ));
  }

  _onFetchPosts(GetAllPostApiEvent event, Emitter<HomeFeedState> emit) async {
    startPerformanceTimer('fetch_posts');

    if (event.page == 1) {
      emit(state.copyWith(homeFeedLoading: true));
    } else {
      emit(state.copyWith(isLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await timeOperation(
        'api_get_all_posts',
        () => apiClient.getAllPost(loginuserId, brandId.toString(), event.page, '2'),
      );
      if (results.results.status == true) {
        final newPosts = results.results.posts;

        // Use optimized filtering to prevent O(n²) complexity
        final filteredNewPosts = ListUtils.filterDuplicates(
          state.posts,
          newPosts,
          (post) => post.id,
        );

        // Create new list instead of mutating state
        final updatedPosts = ListUtils.mergeListsEfficiently(
          state.posts,
          filteredNewPosts,
          (post) => post.id,
        );

        // Clean up old posts if list gets too large (prevent memory bloat)
        final finalPosts = MemoryUtils.shouldCleanupList(updatedPosts, 1000)
            ? ListUtils.cleanupOldItems(updatedPosts, 800)
            : updatedPosts;

        Logger.lOG("TOTAL POST LENGTH : ${finalPosts.length}");

        emit(state.copyWith(
            posts: finalPosts,
            postPage: event.page,
            allFetch: false,
            homeFeedLoading: false,
            postResponsemodel: results,
            isLoadingMore: false));
      } else {
        emit(state.copyWith(isLoadingMore: true, allFetch: true, homeFeedLoading: false));
      }
      endPerformanceTimer('fetch_posts');
    } catch (e) {
      endPerformanceTimer('fetch_posts');

      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, allFetch: true, homeFeedLoading: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, homeFeedLoading: false));
      });
    }
  }

  _onFetchVideo(GetAllVideoApiEvent event, Emitter<HomeFeedState> emit) async {
    if (event.page == 1) {
      emit(state.copyWith(homeFeedVideoLoading: true));
    } else {
      emit(state.copyWith(isVideoLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await apiClient.getAllVideo(loginuserId, brandId.toString(), event.page, 1.toString());
      if (results.results.status == true) {
        final newVideo = results.results.data;

        // Use optimized filtering for videos
        final filteredNewVideos = ListUtils.filterDuplicates(
          state.video,
          newVideo,
          (video) => video.id,
        );

        // Create new list instead of mutating state
        final updatedVideos = ListUtils.mergeListsEfficiently(
          state.video,
          filteredNewVideos,
          (video) => video.id,
        );

        // Clean up old videos if list gets too large
        final finalVideos = MemoryUtils.shouldCleanupList(updatedVideos, 500)
            ? ListUtils.cleanupOldItems(updatedVideos, 400)
            : updatedVideos;

        Logger.lOG("TOTAL Video LENGTH : ${finalVideos.length}");

        emit(state.copyWith(
            video: finalVideos,
            videoPage: event.page,
            allVideoFetch: false,
            homeFeedVideoLoading: false,
            videoResponseModel: results,
            isVideoLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isVideoLoadingMore: false, allVideoFetch: true, homeFeedVideoLoading: false));
      });
    }
  }

// ================== Like Post Socket Event ==================
  _onLikePostSocket(LikePostSocketEvent event, Emitter<HomeFeedState> emit) async {
    final completer = Completer<Map<String, dynamic>>();
    try {
      // Emit the event to the socket
      SocketService.emit(
          APIConfig.likePost, {'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN), 'post_id': event.postId});
      // Create a callback variable to keep track of whether the completer is completed
      bool isCompleted = false;
      // Listen for the response from the socket
      SocketService.response(
        APIConfig.likePost,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      final postId = response['post_id'];
      final likes = response['likes_count'];
      final isLiked = response['is_liked'];
      await _onupdatePostLikeEvent(UpdatePostLikesEvent(isLiked: isLiked, likes: likes, postId: postId), emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onupdatePostLikeEvent(UpdatePostLikesEvent event, Emitter<HomeFeedState> emit) {
    try {
      final updatedPosts = List.of(state.posts);
      for (int i = 0; i < updatedPosts.length; i++) {
        if (updatedPosts[i].id == event.postId) {
          updatedPosts[i] = updatedPosts[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      //Video
      final updatedVideos = List.of(state.video);
      for (int i = 0; i < updatedVideos.length; i++) {
        if (updatedVideos[i].id == event.postId) {
          updatedVideos[i] = updatedVideos[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      //discover post
      final discoverPosts = List.of(state.isDiscoverposts);
      for (int i = 0; i < discoverPosts.length; i++) {
        if (discoverPosts[i].id == event.postId) {
          discoverPosts[i] = discoverPosts[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }

      //Save Post
      final updatedsavePost = List.of(state.savedposts);
      for (int i = 0; i < updatedsavePost.length; i++) {
        if (updatedsavePost[i].id == event.postId) {
          updatedsavePost[i] = updatedsavePost[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      // User Post By ID
      final updatedUserPostbyidPosts = List.of(state.isuserProfileposts);
      for (int i = 0; i < updatedUserPostbyidPosts.length; i++) {
        if (updatedUserPostbyidPosts[i].id == event.postId) {
          updatedUserPostbyidPosts[i] =
              updatedUserPostbyidPosts[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      // Get User Post
      final getprofilePosts = List.of(state.getProfilePost);
      for (int i = 0; i < getprofilePosts.length; i++) {
        if (getprofilePosts[i].id == event.postId) {
          getprofilePosts[i] = getprofilePosts[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      // Get User Video
      final getprofileVideos = List.of(state.getProfilevideo);
      for (int i = 0; i < getprofileVideos.length; i++) {
        if (getprofileVideos[i].id == event.postId) {
          getprofileVideos[i] = getprofileVideos[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      // Get User Video
      final getprofileByIdVideos = List.of(state.getProfileByIDvideo);
      for (int i = 0; i < getprofileByIdVideos.length; i++) {
        if (getprofileByIdVideos[i].id == event.postId) {
          getprofileByIdVideos[i] = getprofileByIdVideos[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      // Get User tag Post
      final getprofileTagPost = List.of(state.tagPostData);
      for (int i = 0; i < getprofileTagPost.length; i++) {
        if (getprofileTagPost[i].id == event.postId) {
          getprofileTagPost[i] = getprofileTagPost[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
//Hashtag post
      final hashTagPosts = List.of(state.hashtagPostData);
      for (int i = 0; i < hashTagPosts.length; i++) {
        if (hashTagPosts[i].id == event.postId) {
          hashTagPosts[i] = hashTagPosts[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }

      //Notification Post
      final updatedNotificationPost = List.of(state.getpostbyIdData);
      for (int i = 0; i < updatedNotificationPost.length; i++) {
        if (updatedNotificationPost[i].id == event.postId) {
          updatedNotificationPost[i] = updatedNotificationPost[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      //Notification Post
      final updatedDeepLinkPost = state.deepLinkPostModel != null ? [state.deepLinkPostModel!] : [];
      for (int i = 0; i < updatedDeepLinkPost.length; i++) {
        if (updatedDeepLinkPost[i].id == event.postId) {
          updatedDeepLinkPost[i] = updatedDeepLinkPost[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }
      //Text Post
      final usertextpost = List.of(state.getUserTextPostData);
      for (int i = 0; i < usertextpost.length; i++) {
        if (usertextpost[i].id == event.postId) {
          usertextpost[i] = usertextpost[i].copyWith(likes: event.likes, isLiked: event.isLiked);
          break;
        }
      }

      //Reels Post
      // final reelsPost = List.of(state.reels);
      // for (int i = 0; i < reelsPost.length; i++) {
      //   if (reelsPost[i].id == event.postId) {
      //     reelsPost[i] = reelsPost[i].copyWith(likes: event.likes, isLiked: event.isLiked);
      //     break;
      //   }
      // }

      emit(state.copyWith(
        posts: updatedPosts,
        video: updatedVideos,
        isDiscoverposts: discoverPosts,
        savedposts: updatedsavePost,
        isuserProfileposts: updatedUserPostbyidPosts,
        getProfilePost: getprofilePosts,
        getProfilevideo: getprofileVideos,
        getProfileByIDvideo: getprofileByIdVideos,
        tagPostData: getprofileTagPost,
        hashtagPostData: hashTagPosts,
        getpostbyIdData: updatedNotificationPost,
        getUserTextPostData: usertextpost,
        deepLinkPostModel: updatedDeepLinkPost.isNotEmpty ? updatedDeepLinkPost.first : null,
        // reels: reelsPost,
      ));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

//=================== Post Comment ===================//
  _onCommentGetApi(GetCommentApiEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      emit(state.copyWith(isCommentloding: true));
      final results = await apiClient.getCommentById(loginuserId, event.postId.toString(), brandId.toString());
      if (results.status == true) {
        emit(state.copyWith(getcommentModel: results, isCommentloding: false));
      }
    } catch (e) {
      emit(state.copyWith(isCommentloding: false));
      Logger.lOG(e.toString());
    }
  }

  _oncommentPostSocket(
    CommentPostSocketEvent event,
    Emitter<HomeFeedState> emit,
  ) async {
    final completer = Completer<Map<String, dynamic>>();
    try {
      SocketService.emit(APIConfig.postcomment, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'post_id': event.postId,
        'comment_text': event.commentText
      });
      bool isCompleted = false;

      SocketService.response(
        APIConfig.postcomment,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );

      final response = await completer.future;
      final data = response['data'];
      final id = data['id'];
      final commentText = data['comment_text'];
      final createdAt = data['created_at'];
      final user = data['user'];
      final likesCount = data['likes_count'];
      final commentCount = data['comment_count'];
      final latestcomment = data['latestcomment'];
      await _onupdatePostCommentEvent(
          UpdatePostCommentEvent(
              data: data,
              id: id,
              commentText: commentText,
              createdAt: createdAt,
              user: user,
              likesCount: likesCount,
              commentCount: commentCount,
              postId: event.postId,
              latestcomment: latestcomment),
          emit);

      // Clear the comment text controller
      // state.commentTextController?.clear();
    } catch (e) {
      Logger.lOG(e.toString());
      // state.commentTextController?.clear();
    }
  }

  _onupdatePostCommentEvent(UpdatePostCommentEvent event, Emitter<HomeFeedState> emit) {
    try {
      final newComment = CommentData(
          id: event.id,
          commentText: event.commentText,
          createdAt: event.createdAt,
          isLiked: false,
          user: CommentUser(
            id: event.user['id'],
            username: event.user['username'],
            profileImage: event.user['profile_image'] == null
                ? event.user['id'].toString() == Prefobj.preferences?.get(Prefkeys.USER_ID).toString()
                    ? AssetConstants.pngUser
                    : AssetConstants.pngUserReomve
                : APIConfig.mainbaseURL + event.user['profile_image'],
          ),
          likesCount: event.likesCount,
          replies: []);
      final updatedCommentModel = state.getcommentModel?.copyWith(
        comments: [
          ...?state.getcommentModel?.comments,
          newComment,
        ],
      );
      emit(state.copyWith(getcommentModel: updatedCommentModel));
      final updatedPosts = List.of(state.posts);
      for (int i = 0; i < updatedPosts.length; i++) {
        if (updatedPosts[i].id == int.tryParse(event.postId)) {
          updatedPosts[i] = updatedPosts[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final updatedSavePosts = List.of(state.savedposts);
      for (int i = 0; i < updatedSavePosts.length; i++) {
        if (updatedSavePosts[i].id == int.tryParse(event.postId)) {
          updatedSavePosts[i] = updatedSavePosts[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final updateduserpostbyidPosts = List.of(state.isuserProfileposts);
      for (int i = 0; i < updateduserpostbyidPosts.length; i++) {
        if (updateduserpostbyidPosts[i].id == int.tryParse(event.postId)) {
          updateduserpostbyidPosts[i] = updateduserpostbyidPosts[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final updatedVideo = List.of(state.video);
      for (int i = 0; i < updatedVideo.length; i++) {
        if (updatedVideo[i].id == int.tryParse(event.postId)) {
          updatedVideo[i] = updatedVideo[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final updateduserpostbyidVideo = List.of(state.getProfileByIDvideo);
      for (int i = 0; i < updateduserpostbyidVideo.length; i++) {
        if (updateduserpostbyidVideo[i].id == int.tryParse(event.postId)) {
          updateduserpostbyidVideo[i] = updateduserpostbyidVideo[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final isDiscoverItem = List.of(state.isDiscoverposts);
      for (int i = 0; i < isDiscoverItem.length; i++) {
        if (isDiscoverItem[i].id == int.tryParse(event.postId)) {
          isDiscoverItem[i] = isDiscoverItem[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final getProfilePost = List.of(state.getProfilePost);
      for (int i = 0; i < getProfilePost.length; i++) {
        if (getProfilePost[i].id == int.tryParse(event.postId)) {
          getProfilePost[i] = getProfilePost[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final getProfilevideo = List.of(state.getProfilevideo);
      for (int i = 0; i < getProfilevideo.length; i++) {
        if (getProfilevideo[i].id == int.tryParse(event.postId)) {
          getProfilevideo[i] = getProfilevideo[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final updatedNotificationPost = List.of(state.getpostbyIdData);
      for (int i = 0; i < updatedNotificationPost.length; i++) {
        if (updatedNotificationPost[i].id == int.tryParse(event.postId)) {
          updatedNotificationPost[i] = updatedNotificationPost[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }
      final updatedDeepLinkPost = state.deepLinkPostModel != null ? [state.deepLinkPostModel!] : [];
      for (int i = 0; i < updatedDeepLinkPost.length; i++) {
        if (updatedDeepLinkPost[i].id == int.tryParse(event.postId)) {
          updatedDeepLinkPost[i] = updatedDeepLinkPost[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }

      final updateduserTextPost = List.of(state.getUserTextPostData);
      for (int i = 0; i < updateduserTextPost.length; i++) {
        if (updateduserTextPost[i].id == int.tryParse(event.postId)) {
          updateduserTextPost[i] = updateduserTextPost[i].copyWith(
            commentsCount: event.commentCount,
            latestComment: event.latestcomment,
          );
          break;
        }
      }

      //Reels Post
      // final reelsPost = List.of(state.reels);
      // for (int i = 0; i < reelsPost.length; i++) {
      //   if (reelsPost[i].id == int.tryParse(event.postId)) {
      //     reelsPost[i] = reelsPost[i].copyWith(
      //       commentsCount: event.commentCount,
      //       latestComment: event.latestcomment,
      //     );
      //     break;
      //   }
      // }

      emit(state.copyWith(
        posts: updatedPosts,
        savedposts: updatedSavePosts,
        isuserProfileposts: updateduserpostbyidPosts,
        video: updatedVideo,
        getProfileByIDvideo: updateduserpostbyidVideo,
        isDiscoverposts: isDiscoverItem,
        getProfilePost: getProfilePost,
        getProfilevideo: getProfilevideo,
        getpostbyIdData: updatedNotificationPost,
        getUserTextPostData: updateduserTextPost,
        deepLinkPostModel: updatedDeepLinkPost.isNotEmpty ? updatedDeepLinkPost.first as DeepLinkPostModel : null,
        // reels: reelsPost,
      ));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onreplycommentSocket(
    ReplycommentSocketEvent event,
    Emitter<HomeFeedState> emit,
  ) async {
    final completer = Completer<Map<String, dynamic>>();
    try {
      SocketService.emit(APIConfig.replycomment, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'comment_id': event.commentId,
        'comment_text': event.replyText
      });
      bool isCompleted = false;

      SocketService.response(
        APIConfig.replycomment,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      final data = response['data'];
      final commentid = data['main_comment_id'];
      final replayid = data['id'];
      final replyCommentText = data['comment_text'];
      final likecount = data['likes_count'];
      final createdAt = data['created_at'];
      final user = data['user'];
      _onupdatereplyCommentEvent(
          UpdateReplyCommenEvent(
              commentid: commentid,
              replyid: replayid,
              likecount: likecount,
              createdAt: createdAt,
              user: user,
              replyCommentText: replyCommentText),
          emit);

      // state.commentTextController?.clear();
    } catch (e) {
      Logger.lOG(e.toString());
      // state.replycommentTextController?.clear();
    }
  }

  _onupdatereplyCommentEvent(UpdateReplyCommenEvent event, Emitter<HomeFeedState> emit) async {
    try {
      final updatedCommentModel = state.getcommentModel?.copyWith(
        comments: state.getcommentModel?.comments.map((comment) {
          final updatedReplies = comment.replies;
          if (comment.id == event.commentid) {
            updatedReplies.add(
              ReplyData(
                id: event.replyid,
                replyText: event.replyCommentText,
                isLiked: false,
                likesCount: event.likecount,
                user: CommentUser(
                  id: event.user['id'],
                  username: event.user['username'],
                  profileImage: event.user['profile_image'] == null
                      ? AssetConstants.pngUser
                      : APIConfig.mainbaseURL + event.user['profile_image'],
                ),
                createdAt: event.createdAt,
              ),
            );
          }
          return comment.copyWith(replies: updatedReplies);
        }).toList(),
      );

      emit(state.copyWith(getcommentModel: updatedCommentModel));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onlikeCommentsocket(
    LikeCommentSocketEvent event,
    Emitter<HomeFeedState> emit,
  ) async {
    final completer = Completer<Map<String, dynamic>>();
    try {
      // Emit the event to the socket
      SocketService.emit(APIConfig.likecomment, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'comment_id': event.commentId,
      });

      // Create a callback variable to keep track of whether the completer is completed
      bool isCompleted = false;

      // Listen for the response from the socket
      SocketService.response(
        APIConfig.likecomment,
        (response) {
          if (!isCompleted) {
            // Complete the completer with the response
            completer.complete(response);

            isCompleted = true; // Set isCompleted to true to avoid multiple completions
          }
        },
      );

      // Await the response
      final response = await completer.future;

      final data = response['data'];
      final commentid = data['comment_id'];
      final likecount = data['comment_likes'];
      final isliked = response['is_liked'];

      _onupdateCommentLikeEvent(
          UpdateCommenLikeEvent(commentid: commentid, likecount: likecount, isliked: isliked), emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onupdateCommentLikeEvent(UpdateCommenLikeEvent event, Emitter<HomeFeedState> emit) {
    try {
      final updatedCommentModel = state.getcommentModel?.copyWith(
          comments: state.getcommentModel?.comments.map((comment) {
        if (comment.id == event.commentid) {
          // Replace the comment's is_liked and commentlikes properties
          return comment.copyWith(isLiked: event.isliked, likesCount: event.likecount);
        }
        // Return the comment as is if it doesn't match
        return comment;
      }).toList());
      emit(state.copyWith(getcommentModel: updatedCommentModel));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onreplayCommentlikeSocket(
    ReplayCommentLikeSocketEvent event,
    Emitter<HomeFeedState> emit,
  ) async {
    try {
      final completer = Completer<Map<String, dynamic>>();
      SocketService.emit(APIConfig.replycommentlike, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'reply_comment_id': event.commentId,
      });
      bool isCompleted = false;
      SocketService.response(
        APIConfig.replycommentlike,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      final data = response['data'];
      final commentid = data['comment_id'];
      final likecount = data['comment_likes'];
      final isLiked = response['is_liked'];

      await _onupdatereplyCommentLikeEvent(
          UpdateReplyCommenLikeEvent(commentid: commentid, likecount: likecount, isLiked: isLiked), emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onupdatereplyCommentLikeEvent(
    UpdateReplyCommenLikeEvent event,
    Emitter<HomeFeedState> emit,
  ) {
    try {
      final updatedCommentModel = state.getcommentModel?.copyWith(
        comments: state.getcommentModel?.comments.map((comment) {
          if (comment.replies.isNotEmpty) {
            for (int i = 0; i < comment.replies.length; i++) {
              if (comment.replies[i].id == event.commentid) {
                final updatedReplies = List.of(comment.replies);
                updatedReplies[i] = updatedReplies[i].copyWith(
                  likesCount: event.likecount,
                  isLiked: event.isLiked,
                );
                return comment.copyWith(replies: updatedReplies);
              }
            }
          }
          return comment;
        }).toList(),
      );

      emit(state.copyWith(getcommentModel: updatedCommentModel));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onDeleteCommentApi(DeleteCommentApiEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';

      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await apiClient.deleteCommentById(loginuserId, event.commentId.toString(), brandId.toString());
      if (results.status == true) {
        await _onRefreshCommentGetApi(RefreshCommentGetApiEvent(postId: event.postId), emit);
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onRefreshCommentGetApi(RefreshCommentGetApiEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final results = await apiClient.getCommentById(loginuserId, event.postId, brandId.toString());
      if (results.status == true) {
        emit(state.copyWith(getcommentModel: results));
        NavigatorService.goBack();
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  // Delete Post
  // _onDeletePostApi(DeletePostApiEvent event, Emitter<HomeFeedState> emit) async {
  //   try {
  //     emit(state.copyWith(isDeleteLoading: true));
  //     final results = await apiClient.deletePost(event.postId);
  //     if (event.isprofilrpostDelete == true) {
  //       event.context!.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false));
  //     }
  //     if (results.status == true) {
  //       if (event.isPost == true) {
  //         state.posts.removeAt(event.index);
  //       }
  //       if (event.userpost == true) {
  //         state.getProfilePost.removeAt(event.index);
  //       }
  //       if (event.isVideo == true) {
  //         state.video.removeAt(event.index);
  //       }
  //       if (event.userByIDpost == true) {
  //         state.isuserProfileposts.removeAt(event.index);
  //       }
  //       if (event.userVideo == true) {
  //         state.getProfilevideo.removeAt(event.index);
  //       }
  //       if (event.userByIDvideo == true) {
  //         state.getProfileByIDvideo.removeAt(event.index);
  //       }
  //       if (event.isTextPost == true) {
  //         state.getUserTextPostData.removeAt(event.index);
  //       }
  //       NavigatorService.goBack();

  //       emit(state.copyWith(isDeleteLoading: false));
  //     } else {
  //       emit(state.copyWith(isDeleteLoading: false));
  //       NavigatorService.goBack();
  //     }
  //   } catch (e) {
  //     Logger.lOG(e.toString());
  //     emit(state.copyWith(isDeleteLoading: false));
  //     NavigatorService.goBack();
  //   }
  // }
  _onDeletePostApi(DeletePostApiEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isDeleteLoading: true));
      final results = await apiClient.deletePost(loginuserId, event.postId, brandId.toString());

      if (event.isprofilrpostDelete == true) {
        event.context!.read<UserProfileBloc>().add(FetchUserProfileEvent(onEdit: false));
      }

      if (results.status == true) {
        final postIdToRemove = event.postId;

        // Remove from all lists by postId
        state.posts.removeWhere((post) => post.id == postIdToRemove);
        state.getProfilePost.removeWhere((post) => post.id == postIdToRemove);
        state.video.removeWhere((post) => post.id == postIdToRemove);
        state.isuserProfileposts.removeWhere((post) => post.id == postIdToRemove);
        state.getProfilevideo.removeWhere((post) => post.id == postIdToRemove);
        state.getProfileByIDvideo.removeWhere((post) => post.id == postIdToRemove);
        state.getUserTextPostData.removeWhere((post) => post.id == postIdToRemove);

        NavigatorService.goBack();
        if (event.isNotificationPost == true) {
          NavigatorService.goBack();
        }
        emit(state.copyWith(isDeleteLoading: false));
      } else {
        emit(state.copyWith(isDeleteLoading: false));
        NavigatorService.goBack();
        if (event.isNotificationPost == true) {
          NavigatorService.goBack();
        }
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isDeleteLoading: false));
      NavigatorService.goBack();
    }
  }

// save Post
  _savedPostSocketEvent(SavedPostSocketEvent event, Emitter<HomeFeedState> emit) async {
    final completer = Completer<Map<String, dynamic>>();
    try {
      // Emit the event to the socket
      SocketService.emit(APIConfig.savedPost,
          {'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN), 'post_id': event.postId});
      // Create a callback variable to keep track of whether the completer is completed
      bool isCompleted = false;
      // Listen for the response from the socket
      SocketService.response(
        APIConfig.savedPost,
        (response) {
          if (!isCompleted) {
            // Complete the completer with the response
            completer.complete(response);
            isCompleted = true; // Set isCompleted to true to avoid multiple completions
          }
        },
      );

      // Await the response
      final response = await completer.future;
      final message = response['message'];
      final isSaved = response['is_saved'];

      if (message != null) {
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            message.toString(),
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
      }

      await _updateSavedPostSocketEvent(UpdateSavedPostEvent(postId: int.parse(event.postId), isSaved: isSaved), emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _updateSavedPostSocketEvent(UpdateSavedPostEvent event, Emitter<HomeFeedState> emit) {
    try {
      final updatedPosts = state.posts.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final updatedVideos = state.video.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final updated_Posts = state.savedposts.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final profile_posts = state.getProfilePost.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final profile_videos = state.getProfilevideo.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final user_id_by_posts = state.isuserProfileposts.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final user_id_by_video = state.getProfileByIDvideo.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final discover_posts = state.isDiscoverposts.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final hashtag_posts = state.hashtagPostData.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final postbyId_posts = state.getpostbyIdData.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final tag_post_posts = state.tagPostData.map((post) {
        if (post.id == event.postId) {
          return post.copyWith(isSaved: event.isSaved);
        }
        return post;
      }).toList();

      final updatedDeepLinkPost = state.deepLinkPostModel;
      if (updatedDeepLinkPost != null && updatedDeepLinkPost.id == event.postId) {
        final updatedPost = updatedDeepLinkPost.copyWith(isSaved: event.isSaved);
        emit(state.copyWith(deepLinkPostModel: updatedPost));
      }

      //Reels Post
      // final reelsPost = List.of(state.reels);
      // for (int i = 0; i < reelsPost.length; i++) {
      //   if (reelsPost[i].id == event.postId) {
      //     reelsPost[i] = reelsPost[i].copyWith(isSaved: event.isSaved);
      //     break;
      //   }
      // }

      emit(state.copyWith(
        posts: updatedPosts,
        video: updatedVideos,
        savedposts: updated_Posts,
        getProfilePost: profile_posts,
        getProfilevideo: profile_videos,
        isuserProfileposts: user_id_by_posts,
        getProfileByIDvideo: user_id_by_video,
        isDiscoverposts: discover_posts,
        hashtagPostData: hashtag_posts,
        getpostbyIdData: postbyId_posts,
        tagPostData: tag_post_posts,
        // reels: reelsPost,
      ));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _savedPostApiEvent(SavedPostApiEvent event, Emitter<HomeFeedState> emit) async {
    if (event.page == 1) {
      emit(state.copyWith(isSavePostloding: true));
    } else {
      emit(state.copyWith(isLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await apiClient.getSavePost(loginuserId, event.page ?? 0, brandId.toString());

      if (results.results.isNotEmpty) {
        // Get new posts from the API response
        List<SavePostData> savePosts = results.results;

        // Create a new list to avoid modifying the immutable state
        List<SavePostData> updatedPosts = List.from(state.savedposts);

        // Add only unique posts
        for (var post in savePosts) {
          if (!updatedPosts.any((existingPost) => existingPost.id == post.id)) {
            updatedPosts.add(post);
          }
        }

        Logger.lOG("TOTAL SAVED POST LENGTH : ${updatedPosts.length}");

        // Emit the updated state with the new list
        emit(state.copyWith(
          savedposts: updatedPosts,
          savepospage: event.page,
          allFetch: false,
          isSavePostloding: false,
          savepostModel: results,
          isLoadingMore: false,
        ));
      } else {
        emit(state.copyWith(isSavePostLoadingMore: false, savePostallFetch: true, isSavePostloding: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isSavePostLoadingMore: false, savePostallFetch: true, isSavePostloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isSavePostloding: false));
      });
    }
  }

  _ondiscoverPostApiEvent(DiscoverPostApiEvent event, Emitter<HomeFeedState> emit) async {
    if (event.page == 1) {
      emit(state.copyWith(isdiscoverloding: true));
    } else {
      emit(state.copyWith(isDiscoverLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.getDisvoerposts(loginuserId, event.page, brandId.toString());
      if (result.results.status == true) {
        // Get new posts from the API response
        List<PostData> newPosts = result.results.posts;

        // Check for duplicates before adding new posts
        for (var post in newPosts) {
          if (!state.isDiscoverposts.any((existingPost) => existingPost.id == post.id)) {
            state.isDiscoverposts.add(post);
          }
        }
        Logger.lOG("TOTAL DISCOVER POST LENGTH : ${state.isDiscoverposts.length}");

        emit(state.copyWith(
            isDiscoverposts: state.isDiscoverposts,
            isDiscoverpage: event.page,
            isDiscoverallFetch: false,
            isdiscoverloding: false,
            getAllPostModel: result,
            isDiscoverLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isDiscoverLoadingMore: false, isDiscoverallFetch: true, isdiscoverloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isDiscoverallFetch: false, isdiscoverloding: false));
      });
    }
  }

  _onAnalyticsEvent(AnalyticsApiEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(anyliticsLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await apiClient.getAnalyticsData(loginuserId, event.postId, brandId.toString());
      if (results.status == true) {
        emit(state.copyWith(analyticsModel: results, anyliticsLoading: false));
      } else {
        emit(state.copyWith(analyticsModel: results, anyliticsLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(anyliticsLoading: false));
    }
  }

  // ======= User Profile by Id ===============
  _getuserProfileApi(GetUserProfilebyIdApi event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      emit(state.copyWith(profileByIdLoading: true));
      final results = await apiClient.getprofilebyid(loginuserId, event.userId, brandId.toString());
      if (results.status == true) {
        emit(state.copyWith(
          profileByIdLoading: false,
          userProfile: results,
        ));
      } else {
        emit(state.copyWith(profileByIdLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(profileByIdLoading: false));
    }
  }

  _getuserPostApi(GetUserPostApiIdEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      if (event.page == 1) {
        emit(state.copyWith(profilPosteByIdLoading: true));
      } else {
        emit(state.copyWith(profilPosteByIdLoadingmore: true));
      }
      final results = await apiClient.getuserPostbyId(loginuserId, event.page, event.userId ?? 0, brandId.toString());

      if (results.results.status == true) {
        List<PostData> newPosts = results.results.posts;
        newPosts
            .removeWhere((newPost) => state.isuserProfileposts.any((existingPost) => existingPost.id == newPost.id));

        state.isuserProfileposts.addAll(newPosts);

        Logger.lOG("TOTAL USER POST LENGTH : ${state.isuserProfileposts.length}");

        emit(state.copyWith(
            isuserProfileposts: state.isuserProfileposts,
            userpostbyIdPage: event.page,
            profilPosteByIdallFetch: false,
            profilPosteByIdLoading: false,
            getAllUserPostbyidModel: results,
            profilPosteByIdLoadingmore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(profilPosteByIdLoadingmore: false, profilPosteByIdallFetch: true));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(profilPosteByIdallFetch: false));
      });
    }
  }

  // ========= Get User Profile=============
  _getProfilePostApi(FetchUserProfilePostEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      if (event.page == 1) {
        emit(state.copyWith(getprofilrPosteLoading: true));
      } else {
        emit(state.copyWith(getprofilPosteLoadingmore: true));
      }
      // emit(state.copyWith(profilPosteByIdLoadingmore: true));

      // Fetch posts from the API
      final results = await apiClient.getUserposts(loginuserId, event.page, brandId.toString());

      if (results.results.status == true) {
        // Ensure that only unique posts are added to the list
        List<PostData> newPosts = results.results.posts;
        newPosts.removeWhere((newPost) => state.getProfilePost.any((existingPost) => existingPost.id == newPost.id));

        state.getProfilePost.addAll(newPosts);

        Logger.lOG("TOTAL USER POST LENGTH : ${state.getProfilePost.length}");

        emit(state.copyWith(
          getProfilePost: state.getProfilePost,
          getProfilepostpage: event.page,
          getprofilPostedallFetch: false,
          getprofilrPosteLoading: false,
          getProfilePostmodel: results,
          getprofilPosteLoadingmore: false,
        ));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(getprofilPosteLoadingmore: false, getprofilPostedallFetch: true));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(getprofilPostedallFetch: false));
      });
    }
  }

  _onprofileVideo(FetchUserProfileVideoEvent event, Emitter<HomeFeedState> emit) async {
    if (event.page == 1) {
      emit(state.copyWith(getProfileVideoLoading: true));
    } else {
      emit(state.copyWith(getProfileisVideoLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await apiClient.getprofileVideo(loginuserId, event.page, brandId.toString());
      if (results.results.status == true) {
        List<VideoData> newVideo = results.results.data;

        final filteredNewVideos = newVideo.where((newvideo) {
          return !state.getProfilevideo.any((existingvideo) => existingvideo.id == newvideo.id);
        }).toList();

        state.getProfilevideo.addAll(filteredNewVideos);

        Logger.lOG("TOTAL Profile Video LENGTH : ${state.getProfilevideo.length}");

        emit(state.copyWith(
            getProfilevideo: state.getProfilevideo,
            getProfilevideoPage: event.page,
            getProfileallVideoFetch: false,
            getProfileVideoLoading: false,
            getProfilevideoResponseModel: results,
            getProfileisVideoLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(
            getProfileisVideoLoadingMore: false, getProfileallVideoFetch: true, getProfileVideoLoading: false));
      });
    }
  }

  _onprofileVideoByID(GetUserVideoByIdApiEvent event, Emitter<HomeFeedState> emit) async {
    if (event.page == 1) {
      emit(state.copyWith(getProfileVideoByIDLoading: true));
    } else {
      emit(state.copyWith(getProfileisVideoByIDLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await apiClient.getuserVideobyId(loginuserId, event.page, event.userId ?? 0, brandId.toString());
      if (results.results.status == true) {
        List<VideoData> newVideo = results.results.data;

        final filteredNewVideos = newVideo.where((newvideo) {
          return !state.getProfileByIDvideo.any((existingvideo) => existingvideo.id == newvideo.id);
        }).toList();

        state.getProfileByIDvideo.addAll(filteredNewVideos);

        Logger.lOG("TOTAL Profile By ID Video LENGTH : ${state.getProfileByIDvideo.length}");

        emit(state.copyWith(
            getProfileByIDvideo: state.getProfileByIDvideo,
            getProfilevideoByIDPage: event.page,
            getProfileallByIDVideoFetch: false,
            getProfileVideoByIDLoading: false,
            getProfilevideoByIDResponseModel: results,
            getProfileisVideoByIDLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(
            getProfileisVideoByIDLoadingMore: false,
            getProfileallByIDVideoFetch: true,
            getProfileVideoByIDLoading: false));
      });
    }
  }

  _getuserTextPostApi(GetUserTextPostEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      if (event.page == 1) {
        emit(state.copyWith(getuserTextPostLoading: true));
      } else {
        emit(state.copyWith(getUserTextPostLoadingMore: true));
      }
      final results =
          await apiClient.getUserTextpostAPI(logInUserId: loginuserId, page: event.page, brand: brandId.toString());

      if (results.results?.status == true) {
        List<UserTextPostData> newTextPosts = results.results?.data ?? [];
        newTextPosts.removeWhere(
            (newTextPosts) => state.getUserTextPostData.any((existingPost) => existingPost.id == newTextPosts.id));

        state.getUserTextPostData.addAll(newTextPosts);

        Logger.lOG("TOTAL USER TEXt POST LENGTH : ${state.getUserTextPostData.length}");

        emit(state.copyWith(
            getUserTextPostData: state.getUserTextPostData,
            getUserTextPostPage: event.page,
            getUserTextPostAllFetch: false,
            getuserTextPostLoading: false,
            getUserTextPostModel: results,
            // getAllUserPostbyidModel: results,
            getUserTextPostLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(
            getUserTextPostLoadingMore: false, getUserTextPostAllFetch: true, getuserTextPostLoading: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(getUserTextPostAllFetch: false));
      });
    }
  }

  _getUserByIdTextPostApi(GetUserByIdTextPostApiIdEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      if (event.page == 1) {
        emit(state.copyWith(getUserByIdTextPostLoading: true));
      } else {
        emit(state.copyWith(getUserByIdTextPostLoadingMore: true));
      }
      final results = await apiClient.getUserByIdTextpostAPI(
          logInUserId: loginuserId, page: event.page, userId: event.userId, brand: brandId.toString());
/*     List<UserTextPostData>? getUserByIdTextPostData,
    bool? getUserByIdTextPostLoadingMore,
    bool? getUserByIdTextPostAllFetch,
    int? getUserByIdTextPostPage,
   ,*/
      if (results.results?.status == true) {
        List<UserTextPostData> getUserByIdTextPostData = results.results?.data ?? [];
        getUserByIdTextPostData.removeWhere((getUserByIdTextPostData) =>
            state.getUserByIdTextPostData.any((existingPost) => existingPost.id == getUserByIdTextPostData.id));

        state.getUserByIdTextPostData.addAll(getUserByIdTextPostData);

        Logger.lOG("TOTAL USER TEXt POST LENGTH : ${state.getUserByIdTextPostData.length}");

        emit(state.copyWith(
            getUserByIdTextPostData: state.getUserByIdTextPostData,
            getUserByIdTextPostPage: event.page,
            getUserByIdTextPostAllFetch: false,
            getUserByIdTextPostLoading: false,
            getUserTextPostModel: results,
            // getAllUserPostbyidModel: results,
            getUserByIdTextPostLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(
            getUserByIdTextPostLoadingMore: false,
            getUserByIdTextPostAllFetch: true,
            getUserByIdTextPostLoading: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(getUserByIdTextPostAllFetch: false));
      });
    }
  }

// =============== Uploaded Edit Post ==========
  _updatepostapicall(UpdatePostApiEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      // ProgressDialogUtils.showProgressDialog(isCancellable: false);
      final editPostModel =
          await apiClient.editPost(loginuserId, event.postId, event.title, event.description, brandId.toString());
      int postId = int.parse(event.postId);
      Logger.lOG("Post ==============${state.posts}");
      Logger.lOG("postId ==============$postId");
      Logger.lOG("postId ==============${event.isvideo}");

      if (editPostModel.status == true) {
        if (event.isvideo == true) {
          final List<VideoData> updatedVideo = state.video.map((video) {
            return video.id == postId
                ? video.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : video;
          }).toList();
          final List<VideoData> getProfilevideo = state.getProfilevideo.map((video) {
            return video.id == postId
                ? video.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : video;
          }).toList();
          final List<VideoData> getProfileByIDvideo = state.getProfileByIDvideo.map((video) {
            return video.id == postId
                ? video.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : video;
          }).toList();
          final List<GetNotificationPostData> getpostbyIdData = state.getpostbyIdData.map((video) {
            return video.id == postId
                ? video.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : video;
          }).toList();
          final List<PostData> isDiscoverposts = state.isDiscoverposts.map((video) {
            return video.id == postId
                ? video.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : video;
          }).toList();

          emit(state.copyWith(
            video: updatedVideo,
            getProfilevideo: getProfilevideo,
            getProfileByIDvideo: getProfileByIDvideo,
            getpostbyIdData: getpostbyIdData,
            isDiscoverposts: isDiscoverposts,
          ));
        } else {
          final List<PostData> updatedPosts = state.posts.map((post) {
            return post.id == postId
                ? post.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : post;
          }).toList();
          final List<PostData> isuserProfileposts = state.isuserProfileposts.map((post) {
            return post.id == postId
                ? post.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : post;
          }).toList();
          final List<PostData> isDiscoverposts = state.isDiscoverposts.map((post) {
            return post.id == postId
                ? post.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : post;
          }).toList();
          final List<PostData> getProfilePost = state.getProfilePost.map((post) {
            return post.id == postId
                ? post.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : post;
          }).toList();
          final List<GetNotificationPostData> getpostbyIdData = state.getpostbyIdData.map((post) {
            return post.id == postId
                ? post.copyWith(
                    title: event.title,
                    description: event.description,
                  )
                : post;
          }).toList();
          emit(state.copyWith(
            posts: updatedPosts,
            getProfilePost: getProfilePost,
            isDiscoverposts: isDiscoverposts,
            isuserProfileposts: isuserProfileposts,
            getpostbyIdData: getpostbyIdData,
          ));
        }
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  // _onTextPostUpdate(EditTextPostAPIEvent event, Emitter<HomeFeedState> emit) async {
  //   try {
  //     // ProgressDialogUtils.showProgressDialog(isCancellable: false);
  //     final editPostModel = await apiClient.editTextPost(
  //       postId: event.postId.toString(),
  //       title: event.title,
  //       description: event.description,
  //     );
  //     int postId = int.parse(event.postId.toString());
  //     // Logger.lOG("Post ==============${state.posts}");
  //     // Logger.lOG("postId ==============$postId");
  //     // Logger.lOG("postId ==============${event.isvideo}");

  //     if (editPostModel.status == true) {
  //       final List<PostData> updatedPosts = state.posts.map((post) {
  //         return post.id == postId
  //             ? post.copyWith(
  //                 title: event.title,
  //                 description: event.description,
  //               )
  //             : post;
  //       }).toList();
  //       final List<UserTextPostData> getUserTextPostData = state.getUserTextPostData.map((post) {
  //         return post.id == postId
  //             ? post.copyWith(
  //                 title: event.title,
  //                 description: event.description,
  //               )
  //             : post;
  //       }).toList();
  //       final List<UserTextPostData> getUserByIdTextPostData = state.getUserByIdTextPostData.map((post) {
  //         return post.id == postId
  //             ? post.copyWith(
  //                 title: event.title,
  //                 description: event.description,
  //               )
  //             : post;
  //       }).toList();
  //       // final List<PostData> getProfilePost = state.getProfilePost.map((post) {
  //       //   return post.id == postId
  //       //       ? post.copyWith(
  //       //           title: event.title,
  //       //           description: event.description,
  //       //         )
  //       //       : post;
  //       // }).toList();

  //       toastification.show(
  //         type: ToastificationType.success,
  //         showProgressBar: false,
  //         title: Text(
  //           editPostModel.message ?? "",
  //           style: GoogleFonts.montserrat(
  //             fontSize: 12.0.sp,
  //             fontWeight: FontWeight.w500,
  //           ),
  //         ),
  //         autoCloseDuration: const Duration(seconds: 3),
  //       );
  //       emit(state.copyWith(
  //         posts: updatedPosts,
  //         getUserByIdTextPostData: getUserByIdTextPostData,
  //         getUserTextPostData: getUserTextPostData,
  //       ));
  //       // }
  //     }
  //   } catch (e) {
  //     Logger.lOG(e.toString());
  //   }
  // }
  _onTextPostUpdate(EditTextPostAPIEvent event, Emitter<HomeFeedState> emit) async {
    try {
      // First, update the UI immediately (optimistic update)
      int postId = int.parse(event.postId.toString());
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final List<PostData> optimisticPosts = state.posts.map((post) {
        return post.id == postId
            ? post.copyWith(
                title: event.title,
                description: event.description,
              )
            : post;
      }).toList();

      final List<UserTextPostData> optimisticUserTextPosts = state.getUserTextPostData.map((post) {
        return post.id == postId
            ? post.copyWith(
                title: event.title,
                description: event.description,
              )
            : post;
      }).toList();

      final List<UserTextPostData> optimisticUserByIdTextPosts = state.getUserByIdTextPostData.map((post) {
        return post.id == postId
            ? post.copyWith(
                title: event.title,
                description: event.description,
              )
            : post;
      }).toList();

      final List<GetNotificationPostData> getpostbyIdData = state.getpostbyIdData.map((post) {
        return post.id == postId
            ? post.copyWith(
                title: event.title,
                description: event.description,
              )
            : post;
      }).toList();

      final List<PostData> isDiscoverposts = state.isDiscoverposts.map((post) {
        return post.id == postId
            ? post.copyWith(
                title: event.title,
                description: event.description,
              )
            : post;
      }).toList();

      // Emit optimistic state immediately
      emit(state.copyWith(
        posts: optimisticPosts,
        getUserByIdTextPostData: optimisticUserByIdTextPosts,
        getUserTextPostData: optimisticUserTextPosts,
        getpostbyIdData: getpostbyIdData,
        isDiscoverposts: isDiscoverposts,
      ));

      // Then make the API call
      final editPostModel = await apiClient.editTextPost(
        logInUserId: loginuserId,
        postId: event.postId.toString(),
        title: event.title,
        description: event.description,
        brand: brandId.toString(),
      );

      if (editPostModel.status == true) {
        // Show success message
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            editPostModel.message ?? "",
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        // State is already updated optimistically, no need to emit again
      } else {
        // If API call fails, revert the optimistic update
        emit(state.copyWith(
          posts: state.posts, // Original state
          getUserByIdTextPostData: state.getUserByIdTextPostData,
          getUserTextPostData: state.getUserTextPostData,
        ));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      // Revert optimistic update on error
      emit(state.copyWith(
        posts: state.posts,
        getUserByIdTextPostData: state.getUserByIdTextPostData,
        getUserTextPostData: state.getUserTextPostData,
      ));
    }
  }

//=================== Story ===================//
  _onStoryLikeSocket(StoryLikeSocketEvent event, Emitter<HomeFeedState> emit) async {
    final completer = Completer<Map<String, dynamic>>();
    try {
      SocketService.emit(APIConfig.likeunlikestory,
          {'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN), 'story_id': event.storyId});
      bool isCompleted = false;
      SocketService.response(
        APIConfig.likeunlikestory,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      final storyId = response['story_id'];
      final isLiked = response['is_liked'];
      // emit(state.copyWith(newStoryModel:))
      await _onupdateStoryLikeEvent(UpdateStoryLikesEvent(isLiked: isLiked, storyId: storyId), emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onupdateStoryLikeEvent(UpdateStoryLikesEvent event, Emitter<HomeFeedState> emit) {
    try {
      // final updatedPosts = List.of(state.singleStoryData);
      for (int i = 0; i < state.newStoryData.length; i++) {
        for (int j = 0; j < state.newStoryData[i].stories!.length; j++) {
          if (state.newStoryData[i].stories?[j].storyId == event.storyId) {
            state.newStoryData[i].stories?[j].copyWith(isLiked: event.isLiked);
          }
        }
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  // Get Profile post for analytics
  Future<void> _onGetProfilePostEvent(GetProfilePostEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(isGetPostLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getProfilePost(logInUserId: loginuserId, brandid: brandId.toString());
      if (result.status == true) {
        Logger.lOG(result.data.name);
        emit(state.copyWith(userProfileModel: result, isGetPostLoading: false));
      } else {
        Logger.lOG(result.message);
        emit(state.copyWith(isGetPostLoading: false));
        // emit(GetProfilePostFailure(result.message.toString()));
      }
    } catch (error) {
      emit(state.copyWith(isGetPostLoading: false, getPostError: handleError(error)));
    }
  }
  // emit(st(handleError(error)));

  _onNewStory(GetNewStoryApiEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(storyisloding: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await apiClient.getNewStoryApi(logInUserId: loginuserId.toString(), brandid: brandId.toString());
      if (results.status == true) {
        final currentStoryIds = state.newStoryData.map((story) => story.userId).toSet();

        // Filter out stories that are already in the state
        final newStory = (results.data ?? []).where((story) => !currentStoryIds.contains(story.userId)).toList();

        // Create a modifiable copy of the existing list
        final updatedStoryData = state.newStoryData.toList();
        updatedStoryData.addAll(newStory);

        Logger.lOG("TOTAL Story LENGTH : ${updatedStoryData.length}");

        // Cache videos for new stories only
        // for (var i = 0; i < newStory.length; i++) {
        //   for (var j = 0; j < newStory[i].userprofile!.length; j++) {
        //     await cacheVideos(newStory[i].userprofile!, i);
        //   }
        // }

        emit(state.copyWith(
          newStoryModel: results,
          newStoryData: updatedStoryData,
          storyisloding: false,
        ));
      } else {
        emit(state.copyWith(storyisloding: false));
      }
    } catch (e) {
      emit(state.copyWith(
        allFetch: true,
        homeFeedLoading: false,
        storyisloding: false,
      ));
      Logger.lOG(e.toString());
    }
  }

  // cacheVideos(String url, int i) async {
  //   FileInfo? fileInfo = await kpfCacheManager.getFileFromCache(url);
  //   if (fileInfo == null) {
  //     Logger.lOG('downloading file ##------->$url##');
  //     await kpfCacheManager.downloadFile(url);
  //     Logger.lOG('downloaded file ##------->$url##');
  //     if (i + 1 == state.newStoryData.length) {
  //       Logger.lOG('caching finished');
  //     }
  //   }
  // }

  _uploadStory(
    UploadStoryApiEvent event,
    Emitter<HomeFeedState> emit,
  ) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = await Prefobj.preferences?.get(Prefkeys.BRANDID);
      // Simulate initial progress
      await Future.delayed(const Duration(seconds: 1));
      progressNotifier.value = 0.3; // 30% progress

      // Simulate progress between 30% and 50%
      await Future.delayed(const Duration(seconds: 1));
      progressNotifier.value = 0.5; // 50% progress

      // Simulate progress between 50% and 70%
      await Future.delayed(const Duration(seconds: 1));
      progressNotifier.value = 0.7; // 70% progress

      // Simulate progress between 70% and 90%
      await Future.delayed(const Duration(seconds: 1));
      progressNotifier.value = 0.9; // 90% progress

      final result = event.title.isEmpty
          ? await apiClient.uploadStoryApi(
              loginuserId, brandId, event.uploadFiles, event.music, event.title, event.isInstagram, event.isFacebook)
          : await apiClient.uploadStoryasHighlightApi(
              loginuserId, event.uploadFiles, event.music, event.title, brandId.toString());

      if (result.status == true) {
        await Future.delayed(const Duration(seconds: 1));
        progressNotifier.value = 1.0; // 100% progress
        await Future.delayed(const Duration(milliseconds: 500));
        progressNotifier.value = 0.0; // reset progress
        emit(state.copyWith(uploadStoryModel: result));
      }
    } catch (e) {
      progressNotifier.value = 0.0;
      Logger.lOG(e.toString());
    }
  }

  _deleteStory(
    DeleteStoryEvent event,
    Emitter<HomeFeedState> emit,
  ) async {
    emit(state.copyWith(isloding: true));
    try {
      // // Simulate initial progress
      // await Future.delayed(const Duration(seconds: 1));
      // progressNotifier.value = 0.3; // 30% progress

      // // Simulate progress between 30% and 50%
      // await Future.delayed(const Duration(seconds: 1));
      // progressNotifier.value = 0.4; // 50% progress

      // // Simulate progress between 50% and 70%
      // await Future.delayed(const Duration(seconds: 1));
      // progressNotifier.value = 0.6; // 70% progress

      // // Simulate progress between 70% and 90%
      // await Future.delayed(const Duration(seconds: 1));
      // progressNotifier.value = 0.8; // 90% progress

      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = await Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.deleteStory(loginuserId, event.storyId.toString(), brandId.toString());

      if (result.status == true) {
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message,
            style: GoogleFonts.montserrat(
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        ); // Log Dio error
        // await Future.delayed(const Duration(seconds: 2));
        // progressNotifier.value = 1.0; // 100% progress
        // await Future.delayed(const Duration(milliseconds: 500));
        // progressNotifier.value = 0.0; // reset progress

        emit(state.copyWith(isloding: false));
      }
    } catch (e) {
      emit(state.copyWith(isloding: false));
      progressNotifier.value = 0.0;
      Logger.lOG(e.toString());
    }
  }

  _getTagPostApi(GetTagPostApi event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = await Prefobj.preferences?.get(Prefkeys.BRANDID);
      emit(state.copyWith(isLoadingMore: true, isloding: true));

      final tagPostModel = await apiClient.getTagPost(loginuserId, event.tagpage, brandId.toString());

      if (tagPostModel.results?.status == true) {
        final newTagPosts = tagPostModel.results?.data ?? [];

        final filteredNewTagPosts = newTagPosts.where((newTagPost) {
          return !state.tagPostData.any((existingPost) => existingPost.id == newTagPost.id);
        }).toList();

        final updatedTagPostData = [...state.tagPostData, ...filteredNewTagPosts];

        Logger.lOG("TOTAL TAG USER POST LENGTH : ${updatedTagPostData.length}");

        emit(state.copyWith(
          tagPostData: updatedTagPostData,
          tagpage: event.tagpage,
          tagPostmodel: tagPostModel,
          isLoadingMore: false,
          isloding: false,
        ));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, isloding: false));
      });
    }
  }

  _followuserLikeListSocketEvent(FollowUserLikeListSocketEvent event, Emitter<HomeFeedState> emit) async {
    try {
      final completer = Completer<Map<String, dynamic>>();

      // Emit the event to the socket
      SocketService.emit(APIConfig.followuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'to_user_id': event.userId,
      });
      bool isCompleted = false;
      // Listen for the response from the socket
      SocketService.response(
        APIConfig.followuser,
        (response) {
          if (!isCompleted) {
            // Complete the completer with the response
            completer.complete(response);
            isCompleted = true; // Set isCompleted to true to avoid multiple completions
          }
        },
      );

      // Await the response
      final response = await completer.future;
      final followed = response['followed'];
      final followingCount = response['following'];

      _onUpdateFollowUserLikeListEvent(
          UpdateFollowUserLikeListEvent(isFollowing: followed, userId: event.userId, followingcount: followingCount),
          emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onUpdateFollowUserLikeListEvent(UpdateFollowUserLikeListEvent event, Emitter<HomeFeedState> emit) {
    try {
      // Iterate through the current post Like user follow list and update the relevant user's isFollowing status
      final updatedPostLikeList = state.postLikeList.map((postlikeuser) {
        if (postlikeuser.id == event.userId) {
          // Return a new instance of the follower with the updated isFollowing status
          return postlikeuser.copyWith(isFollowing: event.isFollowing);
        }
        return postlikeuser;
      }).toList();

      // Emit the new state with the updated followList
      emit(state.copyWith(postLikeList: updatedPostLikeList));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _followUserSocket(FollowUserSocketEvent event, Emitter<HomeFeedState> emit) async {
    try {
      final completer = Completer<Map<String, dynamic>>();

      // Emit the event to the socket
      SocketService.emit(APIConfig.followuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'to_user_id': event.userId,
      });
      bool isCompleted = false;
      // Listen for the response from the socket
      SocketService.response(
        APIConfig.followuser,
        (response) {
          if (!isCompleted) {
            // Complete the completer with the response
            completer.complete(response);
            isCompleted = true; // Set isCompleted to true to avoid multiple completions
          }
        },
      );

      // Await the response
      final response = await completer.future;
      final followed = response['followed'];
      _onUpdatefollowUserSocket(
          UpdateFollowUserEvent(
            isFollowing: followed,
            userId: event.userId,
          ),
          emit);
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onUpdatefollowUserSocket(UpdateFollowUserEvent event, Emitter<HomeFeedState> emit) {
    try {
      // Ensure to create a new instance of userProfile with updated data
      final updatedUserProfile = state.userProfile?.copyWith(
        data: state.userProfile?.data.copyWith(
          isFollowing: event.isFollowing, // Set the new following status
        ),
      );

      // Emit the new state with updated userProfile
      emit(state.copyWith(userProfile: updatedUserProfile));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  // _blockUserApiCall(
  //   BlockUserApiEvent event,
  //   Emitter<HomeFeedState> emit,
  // ) async {
  //   try {
  //     final blockModel = await apiClient.blockUser({'to_user_id': event.userId});
  //     if (blockModel.status == true) {
  //       if (event.isblocked) {
  //         List<BlockedUsersData> blockeduserList = List.from(state.blockedUsersList);
  //         blockeduserList.removeWhere((user) => user.id.toString() == event.userId);
  //         emit(state.copyWith(blockedUsersList: blockeduserList));
  //       } else {
  //         emit(state.copyWith(blockUserModel: blockModel));
  //         NavigatorService.goBack();
  //       }
  //     }
  //   } catch (e) {
  //     Logger.lOG(e.toString());
  //   }
  // }

  _blockUserApiCall(BlockUserApiEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final blockModel = await apiClient.blockUser(loginuserId, brandId, {'to_user_id': event.userId});
      if (blockModel.status == true) {
        if (event.isblocked) {
          List<BlockedUsersData> blockeduserList = List.from(state.blockedUsersList);
          blockeduserList.removeWhere((user) => user.id.toString() == event.userId);
          emit(state.copyWith(isBlockLoading: false, blockedUsersList: blockeduserList));
        } else {
          List<PostData> updatedPosts = List.from(state.posts);
          updatedPosts.removeWhere((post) => post.user.userId.toString() == event.userId.toString());
          emit(state.copyWith(isBlockLoading: false, blockUserModel: blockModel, posts: updatedPosts));
          NavigatorService.goBack();
        }

        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            blockModel.message ?? "",
            style: Theme.of(event.context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        emit(state.copyWith(isBlockLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isBlockLoading: false));
      NavigatorService.goBack();
      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  _getBlockedUsersListApi(GetblockedusersListApiEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      emit(state.copyWith(isloding: true));

      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final getBlockedUserdListModel = await apiClient.getBlockedUsers(loginuserId, brandId);
      if (getBlockedUserdListModel.status == true) {
        final updatedList = List.of(state.blockedUsersList)..addAll(getBlockedUserdListModel.blockedusersdata ?? []);
        Logger.lOG("TOTAL BLOCKED USERS LIST LENGTH : ${updatedList.length}");
        emit(state.copyWith(allFetch: false, isloding: false, blockedUsersList: updatedList, isLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, allFetch: true, isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isloding: false));
      });
    }
  }

  // ================ HASH TAG POST ===============
  _ondiscoverHashtagPostApiEvent(DiscoverHashtagPostListEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      emit(state.copyWith(isloding: true));

      final getHashTagPostModel =
          await apiClient.getHashtagPostApi(loginuserId, event.hashtagId.toString(), brandId.toString());

      if (getHashTagPostModel.results?.status == true) {
        List<HashtagPostData> hashtagPostData = getHashTagPostModel.results?.data ?? [];

        log("hashtagPostData ======= $hashtagPostData");

        Logger.lOG("TOTAL HASHTAG POST LENGTH : ${state.posts.length}");

        emit(state.copyWith(
            allFetch: false,
            isloding: false,
            hashtagPostModel: getHashTagPostModel,
            hashtagPostData: hashtagPostData,
            isLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, allFetch: true, isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isloding: false));
      });
    }
  }

  // ================ DRAFT POST ===================
  // Get Profile post for analytics
  Future<void> _onGetDraftPostEvent(GetDraftPostAPIEvent event, Emitter<HomeFeedState> emit) async {
    if (event.draftPostPage == 1) {
      emit(state.copyWith(isGetDraftPostLoading: true));
    } else {
      emit(state.copyWith(isGetDraftPostLoadingMore: true));
    }
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result =
          await apiClient.getDraftPost(logInUserId: loginuserId, brandId: brandId, page: event.draftPostPage);
      if (result.results?.status == true) {
        List<DraftPostData> draftPost = result.results?.data ?? [];
        List<DraftPostData> updatedPosts = List.from(state.draftPosts);

        for (var post in draftPost) {
          if (!updatedPosts.any((existingPost) => existingPost.id == post.id)) {
            updatedPosts.add(post);
          }
        }

        Logger.lOG("TOTAL DRAFT POST LENGTH : ${updatedPosts.length}");

        emit(state.copyWith(
            draftPostModel: result,
            draftPosts: updatedPosts,
            isGetDraftPostLoading: false,
            isGetDraftPostLoadingMore: false,
            draftPostPage: event.draftPostPage));
      } else {
        Logger.lOG(result.results?.message);
        emit(state.copyWith(
          isGetDraftPostLoading: false,
          isGetDraftPostLoadingMore: false,
        ));
      }
    } catch (error) {
      emit(state.copyWith(
        isGetDraftPostLoading: false,
        getPostError: handleError(error),
        isGetDraftPostLoadingMore: false,
      ));
    }
  }

  Future<void> _onDeleteDraftPostEvent(DeleteDraftPostAPIEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(isdeleteDraftPostLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.deleteDraftPost(logInUserId: loginuserId, id: event.draftPostId, brand: brandId);

      if (result.status == true) {
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: Theme.of(event.context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        // Remove the deleted post from the list
        final updatedDraftPosts = List<DraftPostData>.from(state.draftPosts)
          ..removeWhere((post) => post.id == event.draftPostId);

        Logger.lOG("POST DELETED SUCCESSFULLY. REMAINING POSTS: ${updatedDraftPosts.length}");

        emit(state.copyWith(
          draftPosts: updatedDraftPosts,
          isdeleteDraftPostLoading: false,
        ));
        NavigatorService.goBack();
      } else {
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: Theme.of(event.context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        NavigatorService.goBack();
        Logger.lOG(result.message);
        emit(state.copyWith(isdeleteDraftPostLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isdeleteDraftPostLoading: false, getPostError: handleError(error)));
    }
  }

  Future<void> _onUploadDraftPostEvent(UploadDraftPostAPIEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(isUploadDraftPostLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result =
          await apiClient.uploadDraftPost(logInUserId: loginuserId, postId: event.draftPostId, brand: brandId);

      if (result.status == true) {
        // Remove the deleted post from the list
        final updatedDraftPosts = List<DraftPostData>.from(state.draftPosts)
          ..removeWhere((post) => post.id == event.draftPostId);
        toastification.show(
          type: ToastificationType.success,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: Theme.of(event.context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );

        Logger.lOG("POST DELETED SUCCESSFULLY. REMAINING POSTS: ${updatedDraftPosts.length}");

        emit(state.copyWith(
          draftPosts: updatedDraftPosts,
          isUploadDraftPostLoading: false,
        ));
        NavigatorService.goBack();
      } else {
        toastification.show(
          type: ToastificationType.error,
          showProgressBar: false,
          title: Text(
            result.message ?? "",
            style: Theme.of(event.context).textTheme.bodyMedium?.copyWith(fontSize: 12.0.sp),
          ),
          autoCloseDuration: const Duration(seconds: 3),
        );
        NavigatorService.goBack();
        Logger.lOG(result.message);
        emit(state.copyWith(isUploadDraftPostLoading: false));
      }
    } catch (error) {
      emit(state.copyWith(isUploadDraftPostLoading: false, getPostError: handleError(error)));
    }
  }

  Future<void> _postShareApiCall(PostShareEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(isPostShareLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final postShare = await apiClient.postShare(
          logInUserId: loginuserId, postId: event.postId, shareType: event.shareType, brand: brandId.toString());
      if (postShare.status == true) {
        emit(state.copyWith(postShareModel: postShare, isPostShareLoading: false));
        if (event.shareType == "fis") {
          event.context.read<SmChatBloc>().add(SendMessageEvent(
                message: postShare.shareUrl,
                file: '',
                touserId: event.toUserId,
                type: 'text',
              ));
        }
      } else {
        emit(state.copyWith(isPostShareLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isPostShareLoading: false));
    }
  }

  Future<void> _sharePostMessageApiCall(SharePostMessageEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(isPostShareLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final sharePostMessage = await apiClient.sharePostMessage(
          logInUserId: loginuserId,
          postId: event.postId,
          toUserId: event.toUserId.toString(),
          brand: brandId.toString());
      if (sharePostMessage.status == true) {
        emit(state.copyWith(sharePostMessageModel: sharePostMessage, isPostShareLoading: false));
      } else {
        emit(state.copyWith(isPostShareLoading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isPostShareLoading: false));
    }
  }

  _ongetNotificationPostApiEvent(GetNotificationPostEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(fetchPostLoading: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final getPostbyIdModel = await apiClient.getPostById(
          logInUserId: loginuserId, postId: event.postId.toString(), brand: brandId.toString());
      if (getPostbyIdModel.results?.status == true) {
        // Create a new modifiable list from the current state
        final updatedList = List.of(state.getpostbyIdData);
        updatedList.addAll(getPostbyIdModel.results?.data ?? []);

        emit(state.copyWith(
          getPostbyIdModel: getPostbyIdModel,
          fetchPostLoading: false,
          getpostbyIdData: updatedList,
        ));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(fetchPostLoading: false));
    }
  }

  Future<void> _fetchDeepLinkPost(FetchDeepLinkPostEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(isDeepLinkPostLoading: true));

    try {
      final url = Uri.parse(
        'https://api.flowkar.com/api/share/post/${event.postId}?share_type=${event.shareType ?? 'fxs'}',
      );
      final result = await http.get(url).timeout(const Duration(seconds: 15));

      if (result.statusCode == 200) {
        final data = json.decode(result.body);

        if (data['status'] == true) {
          final deepLinkPostModel = deserializeDeepLinkPostModel(data['post_data']);
          emit(state.copyWith(isDeepLinkPostLoading: false, deepLinkPostModel: deepLinkPostModel));
        } else {
          emit(state.copyWith(isDeepLinkPostLoading: false));
        }
      } else {
        emit(state.copyWith(isDeepLinkPostLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isDeepLinkPostLoading: false, errorMessage: 'Unexpected error: ${e.toString()}'));
    }
  }

  void _onSearchQueryChanged(UserSearchQueryChanged event, Emitter<HomeFeedState> emit) {
    emit(state.copyWith(searchQuery: event.query));
  }

  void _onClearSearchHistory(ClearUserSearchHistory event, Emitter<HomeFeedState> emit) {
    emit(state.copyWith(searchHistory: []));
  }

  void _onRemoveSearchItem(RemoveUserSearchItem event, Emitter<HomeFeedState> emit) {
    final newHistory = List<Map<String, dynamic>>.from(state.searchHistory);
    newHistory.removeAt(event.index);
    emit(state.copyWith(searchHistory: newHistory));
  }

  _onUserSearchEvent(SharePostSearchUserListEvent event, Emitter<HomeFeedState> emit) async {
    try {
      final completer = Completer<Map<String, dynamic>>();
      SocketService.emit(APIConfig.searchuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'search_text': event.searchtext,
      });
      bool isCompleted = false;

      SocketService.response(
        APIConfig.searchuser,
        (response) {
          if (!isCompleted) {
            completer.complete(response);
            isCompleted = true;
          }
        },
      );
      final response = await completer.future;
      if (response['data'] == null) {
        emit(state.copyWith(searchuserList: []));
      }
      final data = response['data'] as List<dynamic>;
      List<SearchUserData> allUsers = data.map((item) {
        if (item is Map<String, dynamic>) {
          return SearchUserData(
            userId: item['id'],
            name: item['name'],
            userName: item['username'],
            profileImage: item['profile'],
          );
        }
        throw Exception('Invalid data format');
      }).toList();
      final searchText = event.searchtext.toLowerCase();

      if (searchText.isEmpty) {
      } else {
        final filteredUsers = allUsers.where((user) {
          return user.userName?.toLowerCase().contains(searchText) ?? false;
        }).toList();
        Logger.lOG(filteredUsers);
        emit(state.copyWith(searchuserList: filteredUsers));
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _ongetLiveUser(GetLivestoryApiEvent event, Emitter<HomeFeedState> emit) async {
    emit(state.copyWith(liveUserisloding: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final results = await apiClient.getliveuserList(
        logInUserId: loginuserId,
        brand: brandId.toString(),
      );
      if (results.status == true) {
        final currentStoryIds = state.newStoryData.map((story) => story.userId).toSet();

        // Filter out live user that are already in the state
        final newStory = (results.data ?? []).where((story) => !currentStoryIds.contains(story.userId)).toList();

        // Create a modifiable copy of the existing list
        final updatedStoryData = state.livenewStoryData.toList();
        updatedStoryData.addAll(newStory);

        Logger.lOG("TOTAL LIVE USER LENGTH : ${updatedStoryData.length}");

        // Cache videos for new live user only
        // for (var i = 0; i < newStory.length; i++) {
        //   for (var j = 0; j < (newStory[i].userprofile?.length ?? 0); j++) {
        //     await cacheVideos(newStory[i].userprofile![j], i);
        //   }
        // }

        emit(state.copyWith(
          liveUserModel: results,
          liveUserisloding: false,
          livenewStoryData: updatedStoryData,
        ));
      } else {
        emit(state.copyWith(liveUserisloding: false));
      }
    } catch (e) {
      emit(state.copyWith(
        homeFeedLoading: false,
        liveUserisloding: false,
      ));
      Logger.lOG(e.toString());
    }
  }

  /// Progressive loading for posts - displays data as it loads
  _onFetchPostsProgressive(GetAllPostProgressiveEvent event, Emitter<HomeFeedState> emit) async {
    startPerformanceTimer('fetch_posts_progressive');

    if (event.page == 1) {
      emit(state.copyWith(homeFeedLoading: true));
    } else {
      emit(state.copyWith(isLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await timeOperation(
        'api_get_all_posts_progressive',
        () => apiClient.getAllPost(loginuserId, brandId.toString(), event.page, 2.toString()),
      );

      if (results.results.status == true) {
        final newPosts = results.results.posts;

        // Process posts in chunks for progressive display
        await _processPostsProgressively(newPosts, event.page, emit);
      }
      endPerformanceTimer('fetch_posts_progressive');
    } catch (e) {
      endPerformanceTimer('fetch_posts_progressive');
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, allFetch: true, homeFeedLoading: false));
      });
    }
  }

  /// Progressive loading for videos - displays data as it loads
  _onFetchVideoProgressive(GetAllVideoProgressiveEvent event, Emitter<HomeFeedState> emit) async {
    startPerformanceTimer('fetch_videos_progressive');

    if (event.page == 1) {
      emit(state.copyWith(homeFeedVideoLoading: true));
    } else {
      emit(state.copyWith(isVideoLoadingMore: true));
    }

    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final results = await timeOperation(
        'api_get_all_videos_progressive',
        () => apiClient.getAllVideo(loginuserId, brandId.toString(), event.page, 1.toString()),
      );

      if (results.results.status == true) {
        final newVideos = results.results.data;

        // Process videos in chunks for progressive display
        await _processVideosProgressively(newVideos, event.page, emit);
      }
      endPerformanceTimer('fetch_videos_progressive');
    } catch (e) {
      endPerformanceTimer('fetch_videos_progressive');
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isVideoLoadingMore: false, allVideoFetch: true, homeFeedVideoLoading: false));
      });
    }
  }

  /// Process posts progressively in chunks
  Future<void> _processPostsProgressively(List<PostData> newPosts, int page, Emitter<HomeFeedState> emit) async {
    const chunkSize = 3; // Display 3 posts at a time
    const chunkDelay = Duration(milliseconds: 100); // Small delay between chunks

    // Filter duplicates first
    final filteredPosts = ListUtils.filterDuplicates(
      state.posts,
      newPosts,
      (post) => post.id,
    );

    if (filteredPosts.isEmpty) {
      emit(state.copyWith(
        homeFeedLoading: false,
        isLoadingMore: false,
        allFetch: true,
      ));
      return;
    }

    // Start with existing posts or clear for first page
    List<PostData> currentPosts = page == 1 ? [] : List.from(state.posts);

    // Process posts in chunks
    for (int i = 0; i < filteredPosts.length; i += chunkSize) {
      final chunk = filteredPosts.skip(i).take(chunkSize).toList();
      currentPosts.addAll(chunk);

      // Clean up if list gets too large
      if (MemoryUtils.shouldCleanupList(currentPosts, 1000)) {
        currentPosts = ListUtils.cleanupOldItems(currentPosts, 800);
      }

      // Emit state with new chunk
      emit(state.copyWith(
        posts: List.from(currentPosts),
        postPage: page,
        allFetch: false,
        homeFeedLoading: i + chunkSize >= filteredPosts.length ? false : true,
        isLoadingMore: i + chunkSize >= filteredPosts.length ? false : (page > 1),
      ));

      // Small delay to allow UI to update smoothly
      if (i + chunkSize < filteredPosts.length) {
        await Future.delayed(chunkDelay);
      }
    }

    Logger.lOG("PROGRESSIVE POST LOADING COMPLETED: ${currentPosts.length} total posts");
  }

  /// Process videos progressively in chunks
  Future<void> _processVideosProgressively(List<VideoData> newVideos, int page, Emitter<HomeFeedState> emit) async {
    const chunkSize = 2; // Display 2 videos at a time (videos are heavier)
    const chunkDelay = Duration(milliseconds: 150); // Slightly longer delay for videos

    // Filter duplicates first
    final filteredVideos = ListUtils.filterDuplicates(
      state.video,
      newVideos,
      (video) => video.id,
    );

    if (filteredVideos.isEmpty) {
      emit(state.copyWith(
        homeFeedVideoLoading: false,
        isVideoLoadingMore: false,
        allVideoFetch: true,
      ));
      return;
    }

    // Start with existing videos or clear for first page
    List<VideoData> currentVideos = page == 1 ? [] : List.from(state.video);

    // Process videos in chunks
    for (int i = 0; i < filteredVideos.length; i += chunkSize) {
      final chunk = filteredVideos.skip(i).take(chunkSize).toList();
      currentVideos.addAll(chunk);

      // Clean up if list gets too large
      if (MemoryUtils.shouldCleanupList(currentVideos, 500)) {
        currentVideos = ListUtils.cleanupOldItems(currentVideos, 400);
      }

      // Emit state with new chunk
      emit(state.copyWith(
        video: List.from(currentVideos),
        videoPage: page,
        allVideoFetch: false,
        homeFeedVideoLoading: i + chunkSize >= filteredVideos.length ? false : true,
        isVideoLoadingMore: i + chunkSize >= filteredVideos.length ? false : (page > 1),
      ));

      // Small delay to allow UI to update smoothly
      if (i + chunkSize < filteredVideos.length) {
        await Future.delayed(chunkDelay);
      }
    }

    Logger.lOG("PROGRESSIVE VIDEO LOADING COMPLETED: ${currentVideos.length} total videos");
  }

  _getScheduledPostWebApi(GetScheduledPostWebEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isSchedulePostWebLoading: true));
      final results = await apiClient.getScheduledPostWeb(
        logInUserId: loginuserId,
        brand: brandId.toString(),
      );

      if (results.status == true) {
        // Get new posts from the API response
        List<ScheduledPostWebData> scheduledPostData = results.data;

        // Create a new list to avoid modifying the immutable state
        List<ScheduledPostWebData> updatedPosts = List.from(state.schedulePostWeb);

        // Add only unique posts
        for (var post in scheduledPostData) {
          if (!updatedPosts.any((existingPost) => existingPost.id == post.id)) {
            updatedPosts.add(post);
          }
        }

        Logger.lOG("TOTAL SAVED POST LENGTH : ${updatedPosts.length}");

        // Emit the updated state with the new list
        emit(state.copyWith(
          schedulePostWeb: updatedPosts,
          isSchedulePostWebLoading: false,
          schedulePostWebModel: results,
        ));
      } else {
        emit(state.copyWith(isSchedulePostWebLoading: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isSavePostLoadingMore: false, savePostallFetch: true, isSavePostloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isSavePostloding: false));
      });
    }
  }

  // _getPlannerPostListWebApi(GetPlannerPostListEvent event, Emitter<HomeFeedState> emit) async {
  //   try {
  //     String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
  //     int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

  //     if (event.page == 1) {
  //       emit(state.copyWith(isPlannerPostListLoading: true));
  //     } else {
  //       emit(state.copyWith(isPlannerPostListLoadingMore: true));
  //     }

  //     // emit(state.copyWith(isPlannerPostListLoading: false));
  //     final results = await apiClient.getWebList(
  //       logInUserId: loginUserId,
  //       page: event.page,
  //       platform: event.platform,
  //       brand: brandId.toString(),
  //     );

  //     if (results.results.status == true) {
  //       // Get new posts from the API response
  //       List<PlannerPostData> plannerPostData = results.results.data;

  //       // Create a new list to avoid modifying the immutable state
  //       List<PlannerPostData> plannerPostDataUpdate = List.from(state.plannerPostData);

  //       // Add only unique posts
  //       for (var post in plannerPostData) {
  //         if (!plannerPostDataUpdate.any((existingPost) => existingPost.id == post.id)) {
  //           plannerPostDataUpdate.add(post);
  //         }
  //       }

  //       Logger.lOG("TOTAL SAVED POST LENGTH : ${plannerPostDataUpdate.length}");

  //       // Emit the updated state with the new list
  //       emit(state.copyWith(
  //         plannerPostListPage: event.page,
  //         plannerPostData: plannerPostDataUpdate,
  //         isPlannerPostListLoading: false,
  //         isPlannerPostListLoadingMore: false,
  //         plannerListPostModel: results,
  //       ));
  //     } else {
  //       emit(state.copyWith(isPlannerPostListLoading: false, isPlannerPostListLoadingMore: false));
  //     }
  //   } catch (e) {
  //     await Future.delayed(const Duration(seconds: 1), () {
  //       emit(state.copyWith(
  //           isPlannerPostListLoadingMore: false, isPlannerPostListAllFetch: true, isPlannerPostListLoading: false));
  //     });
  //     await Future.delayed(const Duration(seconds: 1), () {
  //       emit(state.copyWith(isPlannerPostListAllFetch: false, isPlannerPostListLoading: false));
  //     });
  //   }
  // }
  _getPlannerPostListWebApi(
    GetPlannerPostListEvent event,
    Emitter<HomeFeedState> emit,
  ) async {
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      if (event.page == 1) {
        // First page → reset everything
        emit(state.copyWith(
          isPlannerPostListLoading: true,
          plannerPostData: [],
          plannerPostListPage: 1,
          isPlannerPostListAllFetch: false,
        ));
      } else {
        emit(state.copyWith(
          isPlannerPostListLoadingMore: true,
        ));
      }

      final results = await apiClient.getWebList(
        logInUserId: loginUserId,
        page: event.page,
        platform: event.platform,
        brand: brandId.toString(),
      );

      if (results.results.status == true) {
        List<PlannerPostData> fetchedPosts = results.results.data;

        // If no posts were fetched, it means no more data
        bool hasMoreData = fetchedPosts.isNotEmpty;

        // Start from existing list
        List<PlannerPostData> updatedList = List.from(state.plannerPostData);

        // Add only unique posts
        for (var post in fetchedPosts) {
          if (!updatedList.any((existingPost) => existingPost.id == post.id)) {
            updatedList.add(post);
          }
        }

        Logger.lOG("TOTAL SAVED POST LENGTH : ${updatedList.length}");

        emit(state.copyWith(
          plannerPostData: updatedList,
          plannerPostListPage: event.page,
          plannerListPostModel: results,
          isPlannerPostListLoading: false,
          isPlannerPostListLoadingMore: false,
          isPlannerPostListAllFetch: !hasMoreData,
        ));
      } else {
        emit(state.copyWith(
          isPlannerPostListLoading: false,
          isPlannerPostListLoadingMore: false,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        isPlannerPostListLoading: false,
        isPlannerPostListLoadingMore: false,
        isPlannerPostListAllFetch: true,
      ));

      await Future.delayed(const Duration(seconds: 1));
      emit(state.copyWith(
        isPlannerPostListAllFetch: false,
      ));
    }
  }

  _aIgenerateCommentApi(AIgenerateCommentEvent event, Emitter<HomeFeedState> emit) async {
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isAIGeneratingMassageloading: true));
      final results = await apiClient.aiGenerateComment(
          logInUserId: loginUserId, brand: brandId.toString(), ccommentText: event.massageText);

      if (results.status == true) {
        emit(state.copyWith(
          isAIGeneratingMassageloading: false,
          aiGenerateMassageOrCommentModel: results,
        ));
      } else {
        emit(state.copyWith(isAIGeneratingMassageloading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isAIGeneratingMassageloading: false));
    }
  }

  _clearAigenerateComment(ClearAIGeneratedCommentEvent event, Emitter<HomeFeedState> emit) {
    emit(state.copyWith(
      aiGenerateMassageOrCommentModel: AiGenerateMassageOrComment(status: true, message: "", result: ""),
    ));
  }
}
