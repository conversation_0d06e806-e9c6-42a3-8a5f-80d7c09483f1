import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/analytics/presentation/widget/line_chart/line_chart_widget.dart';
import 'package:intl/intl.dart';

/// A common widget for displaying analytics data with customizable charts and metrics.
/// This widget can be reused across different parts of the application with different data.
class CommonAnalyticsWidget extends StatefulWidget {
  final String title;
  final String primaryMetricLabel;
  final String graphData2Lable;
  final String tertiaryMetricLabel;
  final Color primaryColor;
  final Color secondaryColor;
  final Color tertiaryColor;
  final List<Map<String, dynamic>>? graphData;
  final List<Map<String, dynamic>>? graphData2;
  final double totalPrimaryMetric;
  final double dailyPrimaryMetric;
  final int totalSecondaryMetric;
  final double dailySecondaryMetric;
  final int totalTertiaryMetric;
  final double dailyTertiaryMetric;
  final bool isLoading;
  final Widget? customContent;
  final Function(DateTime startDate, DateTime endDate)? onDateRangeSubmit;
  final List<Map<String, dynamic>> secondaryMetricsData;
  final String startDate;
  final String endDate;

  const CommonAnalyticsWidget({
    super.key,
    required this.title,
    this.primaryMetricLabel = 'Followers',
    this.tertiaryMetricLabel = 'Posts',
    this.graphData2Lable = "",
    this.primaryColor = const Color(0xFF8D6E63),
    this.secondaryColor = const Color(0xFF006FFF),
    this.tertiaryColor = const Color(0xFF006FFF),
    this.graphData,
    this.graphData2,
    this.totalPrimaryMetric = 0.0,
    this.dailyPrimaryMetric = 0,
    this.totalSecondaryMetric = 0,
    this.dailySecondaryMetric = 0,
    this.totalTertiaryMetric = 0,
    this.dailyTertiaryMetric = 0,
    this.isLoading = false,
    this.customContent,
    this.onDateRangeSubmit,
    this.secondaryMetricsData = const [],
    required this.startDate,
    required this.endDate,
  });

  @override
  State<CommonAnalyticsWidget> createState() => _CommonAnalyticsWidgetState();
}

class _CommonAnalyticsWidgetState extends State<CommonAnalyticsWidget> {
  late DateTime startDate;
  late DateTime endDate;
  DateFormat _dateFormat = DateFormat('dd-MMM-yyyy');

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    final formatter = DateFormat('dd-MM-yyyy');
    final endDateFormatted = formatter.format(now);
    final startDateFormatted = formatter.format(thirtyDaysAgo);
    DateTime startDates =
        DateFormat('dd-MM-yyyy').parse(widget.startDate.isNotEmpty ? widget.startDate : startDateFormatted);
    DateTime endDates = DateFormat('dd-MM-yyyy').parse(widget.endDate.isNotEmpty ? widget.endDate : endDateFormatted);

    setState(() {
      startDate = startDates;
      endDate = endDates;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: widget.isLoading
          ? const Center(child: LoadingAnimationWidget())
          : Column(
              children: [
                _buildHeaderWithDatePicker(),
                Expanded(
                  child: _buildGraph(),
                ),
                _buildPrimaryMetricCard(),
                _buildSecondaryMetricsRow(),
                widget.customContent ?? buildSizedBoxH(20),
              ],
            ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      hasLeadingIcon: true,
      height: 18.h,
      leading: [
        InkWell(
          onTap: () {
            FocusScope.of(context).unfocus();
            NavigatorService.goBack();
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
            child: CustomImageView(
              imagePath: Assets.images.svg.authentication.icBackArrow.path,
              height: 16.h,
            ),
          ),
        ),
        buildSizedBoxW(20.w),
        Text(
          widget.title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 18.sp,
              ),
        ),
      ],
    );
  }

  Widget _buildHeaderWithDatePicker() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Account",
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      letterSpacing: 0.28,
                      fontWeight: FontWeight.w900,
                      color: Theme.of(context).customColors.black.withAlpha(150),
                      fontSize: 16.sp,
                    ),
              ),
              InkWell(
                onTap: () {
                  _showDateRangePicker();
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today_rounded,
                        color: Theme.of(context).primaryColor,
                        size: 16.sp,
                      ),
                      buildSizedBoxW(6.0),
                      Text(
                        '${_dateFormat.format(startDate)} - ${_dateFormat.format(endDate)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).primaryColor,
                              fontSize: 12.sp,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() {
    // Create temporary copies of the dates to work with in the modal
    DateTime tempStartDate = startDate;
    DateTime tempEndDate = endDate;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Make sure it has enough space
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return Container(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Date Range',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: 18.sp,
                        ),
                  ),
                  buildSizedBoxH(20),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Start Date',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).customColors.black.withAlpha(170),
                                    fontSize: 14.sp,
                                  ),
                            ),
                            buildSizedBoxH(8),
                            InkWell(
                              onTap: () async {
                                final DateTime? pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: tempStartDate,
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime.now(),
                                );

                                if (pickedDate != null) {
                                  // Update the state within the modal
                                  setModalState(() {
                                    tempStartDate = pickedDate;

                                    // Ensure end date is not before start date
                                    if (tempEndDate.isBefore(tempStartDate)) {
                                      tempEndDate = tempStartDate;
                                    }
                                  });
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Theme.of(context).primaryColor.withOpacity(0.2),
                                  ),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      _dateFormat.format(tempStartDate),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                            fontSize: 14.sp,
                                          ),
                                    ),
                                    Icon(
                                      Icons.calendar_today_rounded,
                                      size: 18.sp,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      buildSizedBoxW(16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'End Date',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).customColors.black.withAlpha(170),
                                    fontSize: 14.sp,
                                  ),
                            ),
                            buildSizedBoxH(8),
                            InkWell(
                              onTap: () async {
                                final DateTime? pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: tempEndDate,
                                  firstDate: tempStartDate,
                                  lastDate: DateTime.now(),
                                );

                                if (pickedDate != null) {
                                  // Update the state within the modal
                                  setModalState(() {
                                    tempEndDate = pickedDate;
                                  });
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Theme.of(context).primaryColor.withOpacity(0.2),
                                  ),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      _dateFormat.format(tempEndDate),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                            fontSize: 14.sp,
                                          ),
                                    ),
                                    Icon(
                                      Icons.calendar_today_rounded,
                                      size: 18.sp,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  buildSizedBoxH(24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Close bottom sheet first
                        Navigator.pop(context);

                        // Then update the parent widget state with selected dates
                        setState(() {
                          startDate = tempStartDate;
                          endDate = tempEndDate;
                          // Optional debug print to verify dates are updated
                          Logger.lOG(
                              'Updated dates: ${_dateFormat.format(startDate)} - ${_dateFormat.format(endDate)}');
                        });

                        // Call the API with the selected date range
                        if (widget.onDateRangeSubmit != null) {
                          widget.onDateRangeSubmit!(startDate, endDate);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'Apply',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).customColors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildGraph() {
    List<FlSpot> followersData = [];
    List<FlSpot> GraphData2 = [];

    // Use exactly the same conversion logic as the original code
    if (widget.graphData != null) {
      // First conversion method (from original code)
      followersData.addAll((widget.graphData ?? []).map((entry) {
        double x = entry['x']?.toDouble() ?? 0.0;
        double y = entry['y']?.toDouble() ?? 0.0;
        return FlSpot(x, y);
      }));

      // Second conversion method (also from original code)
      for (int i = 0; i < (widget.graphData?.length ?? 0); i++) {
        var followerEntry = widget.graphData?[i];
        double followerValue = (followerEntry?.entries.first.value.toDouble() ?? 0.0);
        followersData.add(FlSpot(i.toDouble(), followerValue));
      }
    }
    if (widget.graphData2 != null) {
      // First conversion method (from original code)
      GraphData2.addAll((widget.graphData2 ?? []).map((entry) {
        double x = entry['x']?.toDouble() ?? 0.0;
        double y = entry['y']?.toDouble() ?? 0.0;
        return FlSpot(x, y);
      }));

      // Second conversion method (also from original code)
      for (int i = 0; i < (widget.graphData2?.length ?? 0); i++) {
        var followerEntry2 = widget.graphData2?[i];
        double followerValue2 = (followerEntry2?.entries.first.value.toDouble() ?? 0.0);
        GraphData2.add(FlSpot(i.toDouble(), followerValue2));
      }
    }

    return LinkedinLineChartWidget(
      data: followersData,
      data2: GraphData2.isNotEmpty ? GraphData2 : null,
      dates: widget.graphData?.map((entry) {
            return entry.map((key, value) => MapEntry(key, value is int ? value : int.tryParse(value.toString()) ?? 0));
          }).toList() ??
          [],
      label: widget.primaryMetricLabel,
      label2: widget.graphData2Lable,
      lineColor: widget.primaryColor,
      fillColor: widget.primaryColor.withOpacity(0.2),
    );
  }

  Widget _buildPrimaryMetricCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.primaryMetricLabel,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).customColors.white,
                    fontSize: 20.sp,
                  ),
            ),
            buildSizedBoxH(4.0),
            Row(
              children: [
                Text(
                  widget.totalPrimaryMetric.toStringAsFixed(2).toString(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                buildSizedBoxW(8.0),
                Icon(
                  Icons.arrow_upward_rounded,
                  color: Theme.of(context).customColors.white,
                  size: 20.sp,
                ),
              ],
            ),
            buildSizedBoxH(4.0),
            if (widget.dailyPrimaryMetric != 0.00)
              Text(
                '+${widget.dailyPrimaryMetric.toStringAsFixed(2)} daily',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).customColors.white.withAlpha(180),
                      fontSize: 16.sp,
                    ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecondaryMetricsRow() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: SizedBox(
        height: 110.h,
        child: Align(
          alignment: Alignment.centerLeft,
          child: ListView.builder(
              padding: EdgeInsets.only(right: 16.w),
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: widget.secondaryMetricsData.length,
              itemBuilder: (_, index) {
                final secondaryMetric = widget.secondaryMetricsData[index];
                return _buildMetricCard(
                  context,
                  secondaryMetric['label'],
                  secondaryMetric['total'].toStringAsFixed(2).toString(),
                  ' ',
                  secondaryMetric['color'],
                  minWidth: widget.secondaryMetricsData.length <= 2 ? 170.w : 0.0,
                );
              }),
        ),
      ),
      // Row(
      //   children: [

      //     Expanded(
      //       child: _buildMetricCard(
      //         context,
      //         widget.secondaryMetricLabel,
      //         widget.totalSecondaryMetric.toString(),
      //         '+${widget.dailySecondaryMetric.toStringAsFixed(2)} daily',
      //         widget.secondaryColor,
      //       ),
      //     ),
      //     buildSizedboxW(16.0),
      //     Expanded(
      //       child: _buildMetricCard(
      //         context,
      //         widget.tertiaryMetricLabel,
      //         widget.totalTertiaryMetric.toString(),
      //         '+${widget.dailyTertiaryMetric.toStringAsFixed(2)} daily',
      //         widget.tertiaryColor,
      //       ),
      //     ),
      //   ],
      // ),
    );
  }

  Widget _buildMetricCard(BuildContext context, String label, String count, String growth, Color color,
      {double? minWidth}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      margin: EdgeInsets.only(left: 16.w),
      constraints: BoxConstraints(minWidth: minWidth ?? 150.w),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).customColors.white,
                  fontSize: 20.sp,
                ),
          ),
          buildSizedBoxH(4.0),
          Row(
            children: [
              Text(
                count,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).customColors.white,
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              buildSizedBoxW(8.0),
              Icon(
                Icons.arrow_upward,
                color: Theme.of(context).customColors.white,
                size: 20.sp,
              ),
            ],
          ),
          buildSizedBoxH(4.0),
          // Text(
          //   growth,
          //   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          //         color: Theme.of(context).customColors.white.withAlpha(170),
          //         fontSize: 16.sp,
          //       ),
          // ),
        ],
      ),
    );
  }
}
