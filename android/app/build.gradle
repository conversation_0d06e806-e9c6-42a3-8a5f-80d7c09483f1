plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services'
    id "com.google.firebase.crashlytics"
}

android {
    namespace = "com.app.flowkar"
    compileSdk = 35
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    defaultConfig {
        applicationId = "com.app.flowkar"
        minSdk = 24
        targetSdk = flutter.targetSdkVersion
        versionCode = 33
        versionName = '1.0.32'
        multiDexEnabled true

        missingDimensionStrategy 'embed', 'v1'

    }

    buildTypes {
        release {
            signingConfig = signingConfigs.debug
        }
    }
    flavorDimensions "environment"
    productFlavors {
        dev {
            dimension "environment"
            // applicationIdSuffix ".dev"
            resValue "string", "app_name", "Flowkar Dev"
            flutter.target "lib/main/main_dev.dart"
        }
        qa {
            dimension "environment"
            // applicationIdSuffix ".qa"
            resValue "string", "app_name", "Flowkar QA"
            flutter.target "lib/main/main_qa.dart"
        }
        prod {
            dimension "environment"
            resValue "string", "app_name", "Flowkar"
            flutter.target "lib/main/main_prod.dart"
        }
    }
}

flutter {
    source = "../.."
}
dependencies {
  implementation platform('com.google.firebase:firebase-bom:33.1.2')
  implementation 'com.google.firebase:firebase-analytics'
  implementation "com.google.firebase:firebase-iid:21.1.0"
}
apply plugin: 'com.google.gms.google-services'
